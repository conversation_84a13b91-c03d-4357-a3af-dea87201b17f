#!/usr/bin/env python3
"""Test script to verify if direct composition eliminates type proliferation"""

import json
from test_direct_composition import LayoutDefinition

def main():
    # Generate schema
    schema = LayoutDefinition.model_json_schema()
    
    # Save to file
    with open('test_direct_composition.schema.json', 'w') as f:
        json.dump(schema, f, indent=2)
    
    print("Schema generated to test_direct_composition.schema.json")
    print(f"Schema has {len(schema.get('$defs', {}))} type definitions")
    
    # Look for any properties that might cause conflicts
    def find_properties(obj, path=""):
        props = []
        if isinstance(obj, dict):
            if 'properties' in obj:
                for prop_name, prop_def in obj['properties'].items():
                    props.append(f"{path}.{prop_name}" if path else prop_name)
            for key, value in obj.items():
                if key != 'properties':
                    props.extend(find_properties(value, f"{path}.{key}" if path else key))
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                props.extend(find_properties(item, f"{path}[{i}]"))
        return props
    
    all_props = find_properties(schema)
    prop_counts = {}
    for prop in all_props:
        simple_name = prop.split('.')[-1]
        prop_counts[simple_name] = prop_counts.get(simple_name, 0) + 1
    
    print("\nProperty name analysis:")
    conflicts = {name: count for name, count in prop_counts.items() if count > 1}
    if conflicts:
        print("Potential conflicts:")
        for name, count in conflicts.items():
            print(f"  {name}: appears {count} times")
    else:
        print("No property name conflicts found!")

if __name__ == "__main__":
    main()
