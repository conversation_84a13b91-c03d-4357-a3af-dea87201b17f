"""
CSDIP Wire Protocol Types - Python Implementation
==============================================

This module defines the Python side of the wire protocol types for CSDIP.
These types are designed to be serialized to JSON and sent to the TypeScript frontend.
"""

from dataclasses import dataclass, field
from pydantic import BaseModel, field_validator
from enum import Enum
from typing import List, Optional, Union
from abc import ABC
import re


# ============================================================================
# ENUMERATIONS
# ============================================================================

class AnimationType(str, Enum):
    SCROLL = "scroll"
    VSCROLL = "vscroll"
    AUTO_SCROLL = "auto-scroll"
    AUTO_VSCROLL = "auto-vscroll"
    BLINK = "blink"
    FLIP = "flip"
    FLIP_BINDINGS = "flip bindings"

class TextBoxType(Enum):
    SINGLELINE = "singleline"
    MULTILINE = "multiline"

class FontWeight(str, Enum):
    VALUE_100 = "100"
    VALUE_200 = "200"
    VALUE_300 = "300"
    VALUE_400 = "400"
    VALUE_500 = "500"
    VALUE_600 = "600"
    VALUE_700 = "700"
    VALUE_800 = "800"
    VALUE_900 = "900"
    LIGHT = "light"
    NORMAL = "normal"
    BOLD = "bold"

class FontStyle(str, Enum):
    NORMAL = "normal"
    ITALIC = "italic"

class FontDecoration(str, Enum):
    NONE = "none"
    UNDERLINE = "underline"
    STRIKEOUT = "strikeout"

class TextCasing(str, Enum):
    LOWER = "lower"
    NORMAL = "normal"
    UPPER = "upper"

class HorizontalAlignment(str, Enum):
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"


class VerticalAlignment(str, Enum):
    TOP = "top"
    CENTER = "center"
    BOTTOM = "bottom"

class BorderStyle(str, Enum):
    NONE = "none"
    SOLID = "solid"
    DOTTED = "dotted"
    DASHED = "dashed"

class StackPanelOrientation(Enum):
    HORIZONTAL = "horizontal"
    VERTICAL = "vertical"


# ============================================================================
# STYLE SYSTEM
# ============================================================================

class Font(BaseModel):
    color: Optional[str] = None  # 6-digit hex color
    family: Optional[str] = None
    size: Optional[int] = None  # max 4 digits
    weight: Optional[FontWeight] = None
    style: Optional[FontStyle] = None
    decoration: Optional[FontDecoration] = None
    
    @field_validator('color')
    @classmethod
    def validate_color(cls, v):
        if v is not None:
            if not re.match(r'^[0-9A-Fa-f]{6}$', v):
                raise ValueError('Color must be a 6-digit hex color (e.g., "FF0000")')
        return v


class Text(BaseModel):
    casing: Optional[TextCasing] = None


class Border(BaseModel):
    color: Optional[str] = None  # 6-digit hex color
    width: Optional[int] = None # non negative integer
    style: Optional[BorderStyle] = None

    @field_validator('color')
    @classmethod
    def validate_color(cls, v):
        if v is not None:
            if not re.match(r'^[0-9A-Fa-f]{6}$', v):
                raise ValueError('Color must be a 6-digit hex color (e.g., "FF0000")')
        return v

    @field_validator('width')
    @classmethod
    def validate_width(cls, v):
        if v is not None and v < 0:
            raise ValueError('Width must be a non-negative integer')
        return v


class Background(BaseModel):
    color: Optional[str] = None  # 6-digit hex color
    image: Optional[str] = None  # image ID reference
    negative: Optional[bool] = None
    transparent: Optional[bool] = None


class AlignmentStyle(BaseModel):
    horizontal: Optional[HorizontalAlignment] = None
    vertical: Optional[VerticalAlignment] = None


class Animation(BaseModel):
    type: AnimationType
    speed: int # min 0
    duration: Optional[int] = None
    offset: Optional[int] = None
    gap: Optional[int] = None


class Style(BaseModel):
    id: str
    font: Optional[Font] = None
    text: Optional[Text] = None
    border: Optional[Border] = None
    background: Optional[Background] = None
    alignment: Optional[AlignmentStyle] = None
    animation: Optional[Animation] = None


@dataclass
class WireInlineStyle:
    id: str
    content: List[Union[str, 'WireInlineStyle']] = field(default_factory=list)


# ============================================================================
# BASE ELEMENT TYPES
# ============================================================================



@dataclass
class WireFormElement(WireStyledElement):
    id: str
    visible: str = "1"  # Bindable expression like "{@var}" or "1"/"0"


@dataclass
class WireFormElementWithCoordinates(WireFormElement):
    width: int
    height: int
    top: int = 0
    left: int = 0


@dataclass
class WireInvisibleFormElement:
    id: str
    # style and visible are prohibited


# ============================================================================
# CONTENT SYSTEM
# ============================================================================

@dataclass
class WireContentParameter:
    name: str
    value: str


@dataclass
class WireContentSet:
    name: str
    content: List[Union[str, WireInlineStyle]] = field(default_factory=list)


@dataclass
class WireContentSetListItem:
    content: List[Union[str, WireInlineStyle]] = field(default_factory=list)


@dataclass
class WireContentSetList:
    name: str
    items: List[WireContentSetListItem] = field(default_factory=list)


@dataclass
class WireContentClear:
    id: Optional[str] = None  # If not provided, clears all


@dataclass
class WireContentInvoke:
    command: str
    target: str


@dataclass
class WireContent:
    form: str  # Form ID
    view: Optional[str] = None  # View ID
    set: List[WireContentSet] = field(default_factory=list)
    setList: List[WireContentSetList] = field(default_factory=list)
    clear: List[WireContentClear] = field(default_factory=list)
    invoke: List[WireContentInvoke] = field(default_factory=list)


# ============================================================================
# FORM ELEMENTS
# ============================================================================

@dataclass
class WireTextRun(WireFormElement):
    text: str  # Bindable


@dataclass
class WireTextSpan(WireFormElement):
    textSpan: List['WireTextSpan'] = field(default_factory=list)
    textRun: List[WireTextRun] = field(default_factory=list)


@dataclass
class WireTextBox(WireFormElementWithCoordinates):
    type: TextBoxType = TextBoxType.SINGLELINE
    text: str  # Bindable


@dataclass
class WireTextBlock(WireFormElementWithCoordinates):
    type: TextBoxType = TextBoxType.SINGLELINE
    textSpan: List[WireTextSpan] = field(default_factory=list)
    textRun: List[WireTextRun] = field(default_factory=list)


@dataclass
class WireImage(WireFormElementWithCoordinates):
    source: str  # Image ID reference


@dataclass
class WireVideo(WireFormElementWithCoordinates):
    source: str  # Video ID reference
    loop: str = "0"  # Bindable boolean


@dataclass
class WireHTMLScrollCommand:
    name: str
    displacement: int
    time: int = 250
    smooth: bool = False
    delay: Optional[int] = None


@dataclass
class WireHTMLExecuteScriptCommand:
    name: str
    value: str  # JavaScript code


@dataclass
class WireHTMLCommands:
    scroll: List[WireHTMLScrollCommand] = field(default_factory=list)
    executeScript: List[WireHTMLExecuteScriptCommand] = field(default_factory=list)


@dataclass
class WireHTML(WireFormElementWithCoordinates):
    source: str  # HTML ID reference
    commands: Optional[WireHTMLCommands] = None


@dataclass
class WireButtonParameter:
    name: str
    value: str


@dataclass
class WireButtonParams:
    param: List[WireButtonParameter] = field(default_factory=list)


@dataclass
class WireButton(WireInvisibleFormElement):
    number: int
    command: str
    target: Optional[str] = None
    params: Optional[WireButtonParams] = None


# Forward declarations for containers
@dataclass
class WirePanel(WireFormElementWithCoordinates):
    panel: List['WirePanel'] = field(default_factory=list)
    stackPanel: List['WireStackPanel'] = field(default_factory=list)
    textBox: List[WireTextBox] = field(default_factory=list)
    textBlock: List[WireTextBlock] = field(default_factory=list)
    image: List[WireImage] = field(default_factory=list)
    video: List[WireVideo] = field(default_factory=list)
    html: List[WireHTML] = field(default_factory=list)


@dataclass
class WireStackPanel(WireFormElementWithCoordinates):
    orientation: StackPanelOrientation = field(default=StackPanelOrientation.VERTICAL)
    panel: List[WirePanel] = field(default_factory=list)
    stackPanel: List['WireStackPanel'] = field(default_factory=list)
    textBox: List[WireTextBox] = field(default_factory=list)
    textBlock: List[WireTextBlock] = field(default_factory=list)
    image: List[WireImage] = field(default_factory=list)
    video: List[WireVideo] = field(default_factory=list)
    html: List[WireHTML] = field(default_factory=list)


class View(WireFormElementWithCoordinates):
    # Note: View doesn't use visible attribute (it's ignored in schema)
    default: bool = False
    homePageTimeout: Optional[int] = None  # in seconds
    panel: List[WirePanel] = field(default_factory=list)
    stackPanel: List[WireStackPanel] = field(default_factory=list)
    textBox: List[WireTextBox] = field(default_factory=list)
    textBlock: List[WireTextBlock] = field(default_factory=list)
    image: List[WireImage] = field(default_factory=list)
    video: List[WireVideo] = field(default_factory=list)
    html: List[WireHTML] = field(default_factory=list)
    button: List[WireButton] = field(default_factory=list)

    def __post_init__(self):
        # View doesn't use the visible attribute
        if hasattr(self, 'visible'):
            delattr(self, 'visible')


# ============================================================================
# ROOT FORM TYPE
# ============================================================================

@dataclass
class Form(WireStyledElement):
    id: str
    style: Optional[str] = None  # Comma-separated style IDs
    view: List[View] = field(default_factory=list)  # At least one required


# ============================================================================
# LAYOUT OPERATIONS
# ============================================================================

@dataclass
class WireAddLayout:
    style: List[Style] = field(default_factory=list)
    form: List[WireForm] = field(default_factory=list)


@dataclass
class WireRemoveLayoutStyle:
    id: str


@dataclass
class WireRemoveLayoutForm:
    id: str


@dataclass
class WireRemoveLayout:
    style: List[WireRemoveLayoutStyle] = field(default_factory=list)
    form: List[WireRemoveLayoutForm] = field(default_factory=list)
    all: Optional[bool] = None


# ============================================================================
# MEDIA OPERATIONS
# ============================================================================

@dataclass
class WireMediaImage:
    id: str
    value: str  # base64 encoded


@dataclass
class WireMediaVideo:
    id: str
    value: str  # base64 encoded


@dataclass
class WireMediaHTML:
    id: str
    value: str  # base64 encoded


@dataclass
class WireMediaCSS:
    id: str
    value: str  # base64 encoded


@dataclass
class WireMediaFonts:
    id: str
    value: str  # base64 encoded


@dataclass
class WireAddMedia:
    image: List[WireMediaImage] = field(default_factory=list)
    video: List[WireMediaVideo] = field(default_factory=list)
    html: List[WireMediaHTML] = field(default_factory=list)
    css: List[WireMediaCSS] = field(default_factory=list)
    fonts: List[WireMediaFonts] = field(default_factory=list)


@dataclass
class WireRemoveMediaImage:
    id: str


@dataclass
class WireRemoveMediaVideo:
    id: str


@dataclass
class WireRemoveMediaHTML:
    id: str


@dataclass
class WireRemoveMediaCSS:
    id: str


@dataclass
class WireRemoveMediaFonts:
    id: str


@dataclass
class WireRemoveMedia:
    image: List[WireRemoveMediaImage] = field(default_factory=list)
    video: List[WireRemoveMediaVideo] = field(default_factory=list)
    html: List[WireRemoveMediaHTML] = field(default_factory=list)
    css: List[WireRemoveMediaCSS] = field(default_factory=list)
    fonts: List[WireRemoveMediaFonts] = field(default_factory=list)
    all: Optional[bool] = None


# ============================================================================
# WIRE PROTOCOL MESSAGES
# ============================================================================

@dataclass
class WireMessage:
    type: str
    payload: Union[WireAddLayout, WireRemoveLayout, WireAddMedia, WireRemoveMedia, WireContent]


# ============================================================================
# UTILITY FUNCTIONS FOR CONVERSION
# ============================================================================

def convert_xml_to_wire_form(xml_form) -> WireForm:
    """Convert XML-based FormDef to wire protocol WireForm"""
    # Implementation would go here to convert from the existing XML model
    # to the new wire protocol format
    pass


def convert_xml_to_wire_style(xml_style) -> Style:
    """Convert XML-based StyleDef to wire protocol Style"""
    # Implementation would go here
    pass


def convert_xml_to_wire_content(xml_content) -> WireContent:
    """Convert XML-based ContentDef to wire protocol WireContent"""
    # Implementation would go here
    pass
