{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CSDIP Display Protocol - Unified Types", "description": "Unified type definitions for CSDIP display protocol with semantic naming", "type": "object", "properties": {"layout_styles": {"items": {"$ref": "#/$defs/StyleDefinition"}, "title": "Layout Styles", "type": "array"}, "layout_forms": {"items": {"$ref": "#/$defs/FormDefinition"}, "title": "Layout Forms", "type": "array"}}, "required": [], "$defs": {"AlignmentStyleDef": {"properties": {"align_horizontal": {"anyOf": [{"$ref": "#/$defs/HorizontalAlignment"}, {"type": "null"}], "default": null}, "align_vertical": {"anyOf": [{"$ref": "#/$defs/VerticalAlignment"}, {"type": "null"}], "default": null}}, "title": "AlignmentStyleDef", "type": "object"}, "AnimationStyleDef": {"properties": {"animation_type": {"$ref": "#/$defs/AnimationType"}, "animation_speed": {"minimum": 0, "title": "Animation Speed", "type": "integer"}, "animation_duration": {"anyOf": [{"minimum": 0, "type": "integer"}, {"type": "null"}], "default": null, "title": "Animation Duration"}, "animation_offset": {"anyOf": [{"minimum": 0, "type": "integer"}, {"type": "null"}], "default": null, "title": "Animation Offset"}, "animation_gap": {"anyOf": [{"minimum": 0, "type": "integer"}, {"type": "null"}], "default": null, "title": "Animation Gap"}}, "required": ["animation_type", "animation_speed"], "title": "AnimationStyleDef", "type": "object"}, "AnimationType": {"enum": ["scroll", "vscroll", "auto-scroll", "auto-vscroll", "blink", "flip", "flip bindings"], "title": "AnimationType", "type": "string"}, "BackgroundStyleDef": {"properties": {"background_color": {"anyOf": [{"description": "Color in hexadecimal format RRGGBB", "type": "string"}, {"type": "null"}], "default": null, "title": "Background Color"}, "background_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Background Image"}, "background_negative": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Background Negative"}, "background_transparent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Background Transparent"}}, "title": "BackgroundStyleDef", "type": "object"}, "BorderStyleDef": {"properties": {"border_color": {"anyOf": [{"description": "Color in hexadecimal format RRGGBB", "type": "string"}, {"type": "null"}], "default": null, "title": "Border Color"}, "border_width": {"anyOf": [{"description": "Pixel value as unsigned short", "maximum": 65535, "minimum": 0, "type": "integer"}, {"type": "null"}], "default": null, "title": "Border Width"}, "border_style": {"anyOf": [{"$ref": "#/$defs/BorderStyleType"}, {"type": "null"}], "default": null}}, "title": "BorderStyleDef", "type": "object"}, "BorderStyleType": {"enum": ["none", "solid", "dotted", "dashed"], "title": "BorderStyleType", "type": "string"}, "ButtonElement": {"description": "Interactive button - no inheritance", "properties": {"button_element_id": {"description": "Unique element identifier", "title": "Button Element Id", "type": "string"}, "button_number": {"maximum": 255, "minimum": 0, "title": "Button Number", "type": "integer"}, "button_command": {"title": "Button Command", "type": "string"}, "button_target": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Button Target"}, "button_params": {"items": {"$ref": "#/$defs/ButtonParameter"}, "title": "Button Params", "type": "array"}}, "required": ["button_element_id", "button_number", "button_command"], "title": "ButtonElement", "type": "object"}, "ButtonParameter": {"properties": {"button_param_name": {"title": "Button Param Name", "type": "string"}, "button_param_value": {"title": "Button Param Value", "type": "string"}}, "required": ["button_param_name", "button_param_value"], "title": "ButtonParameter", "type": "object"}, "Coordinates": {"properties": {"top": {"default": 0, "description": "Pixel value as unsigned short", "maximum": 65535, "minimum": 0, "title": "Top", "type": "integer"}, "left": {"default": 0, "description": "Pixel value as unsigned short", "maximum": 65535, "minimum": 0, "title": "Left", "type": "integer"}, "width": {"description": "Pixel value as unsigned short", "maximum": 65535, "minimum": 0, "title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "height": {"description": "Pixel value as unsigned short", "maximum": 65535, "minimum": 0, "title": "Height", "type": "integer"}}, "required": ["width", "height"], "title": "Coordinates", "type": "object"}, "ExecuteScriptCommand": {"properties": {"html_command_name": {"title": "Html Command Name", "type": "string"}}, "required": ["html_command_name"], "title": "ExecuteScriptCommand", "type": "object"}, "FontDecoration": {"enum": ["none", "underline", "strikeout"], "title": "FontDecoration", "type": "string"}, "FontStyle": {"enum": ["normal", "italic"], "title": "FontStyle", "type": "string"}, "FontStyleDef": {"properties": {"font_color": {"anyOf": [{"description": "Color in hexadecimal format RRGGBB", "type": "string"}, {"type": "null"}], "default": null, "title": "Font Color"}, "font_family": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Font Family"}, "font_size": {"anyOf": [{"description": "Font size", "maximum": 9999, "minimum": 0, "type": "integer"}, {"type": "null"}], "default": null, "title": "Font Size"}, "font_weight": {"anyOf": [{"$ref": "#/$defs/FontWeight"}, {"type": "null"}], "default": null}, "font_style": {"anyOf": [{"$ref": "#/$defs/FontStyle"}, {"type": "null"}], "default": null}, "font_decoration": {"anyOf": [{"$ref": "#/$defs/FontDecoration"}, {"type": "null"}], "default": null}}, "title": "FontStyleDef", "type": "object"}, "FontWeight": {"enum": ["100", "200", "300", "400", "500", "600", "700", "800", "900", "light", "normal", "bold"], "title": "FontWeight", "type": "string"}, "FormDefinition": {"description": "Complete form definition - no inheritance", "properties": {"form_element_id": {"description": "Unique element identifier", "title": "Form Element Id", "type": "string"}, "form_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Form Style Ref"}, "form_views": {"items": {"$ref": "#/$defs/ViewContainer"}, "minItems": 1, "title": "Form Views", "type": "array"}}, "required": ["form_element_id", "form_views"], "title": "FormDefinition", "type": "object"}, "HTMLCommands": {"properties": {"html_commands": {"items": {"anyOf": [{"$ref": "#/$defs/ScrollCommand"}, {"$ref": "#/$defs/ExecuteScriptCommand"}]}, "title": "Html Commands", "type": "array"}}, "title": "HTMLCommands", "type": "object"}, "HTMLDisplay": {"description": "HTML content display - no inheritance", "properties": {"html_element_id": {"description": "Unique element identifier", "title": "Html Element Id", "type": "string"}, "html_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Html Style Ref"}, "html_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Html Visible"}, "html_coordinates": {"$ref": "#/$defs/Coordinates"}, "html_source": {"description": "Text that can contain bindings", "title": "Html Source", "type": "string"}, "html_commands": {"anyOf": [{"$ref": "#/$defs/HTMLCommands"}, {"type": "null"}], "default": null}}, "required": ["html_element_id", "html_coordinates", "html_source"], "title": "HTMLDisplay", "type": "object"}, "HorizontalAlignment": {"enum": ["left", "center", "right"], "title": "HorizontalAlignment", "type": "string"}, "ImageDisplay": {"description": "Image display element - no inheritance", "properties": {"image_element_id": {"description": "Unique element identifier", "title": "Image Element Id", "type": "string"}, "image_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Image Style Ref"}, "image_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Image Visible"}, "image_coordinates": {"$ref": "#/$defs/Coordinates"}, "image_source": {"description": "Text that can contain bindings", "title": "Image Source", "type": "string"}}, "required": ["image_element_id", "image_coordinates", "image_source"], "title": "ImageDisplay", "type": "object"}, "PanelContainer": {"description": "Generic container panel - no inheritance", "properties": {"panel_element_id": {"description": "Unique element identifier", "title": "Panel Element Id", "type": "string"}, "panel_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Panel Style Ref"}, "panel_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Panel Visible"}, "panel_coordinates": {"$ref": "#/$defs/Coordinates"}, "panel_children": {"items": {"anyOf": [{"$ref": "#/$defs/PanelContainer"}, {"$ref": "#/$defs/StackPanelContainer"}, {"$ref": "#/$defs/TextInput"}, {"$ref": "#/$defs/TextDisplay"}, {"$ref": "#/$defs/ImageDisplay"}, {"$ref": "#/$defs/VideoDisplay"}, {"$ref": "#/$defs/HTMLDisplay"}]}, "title": "Panel Children", "type": "array"}}, "required": ["panel_element_id", "panel_coordinates"], "title": "PanelContainer", "type": "object"}, "ScrollCommand": {"properties": {"html_command_name": {"title": "Html Command Name", "type": "string"}}, "required": ["html_command_name"], "title": "ScrollCommand", "type": "object"}, "StackPanelContainer": {"description": "Stack layout container - no inheritance", "properties": {"stackpanel_element_id": {"description": "Unique element identifier", "title": "Stackpanel Element Id", "type": "string"}, "stackpanel_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Stackpanel Style Ref"}, "stackpanel_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Stackpanel Visible"}, "stackpanel_coordinates": {"$ref": "#/$defs/Coordinates"}, "stackpanel_orientation": {"$ref": "#/$defs/StackPanelOrientation"}, "stackpanel_children": {"items": {"anyOf": [{"$ref": "#/$defs/PanelContainer"}, {"$ref": "#/$defs/StackPanelContainer"}, {"$ref": "#/$defs/TextInput"}, {"$ref": "#/$defs/TextDisplay"}, {"$ref": "#/$defs/ImageDisplay"}, {"$ref": "#/$defs/VideoDisplay"}, {"$ref": "#/$defs/HTMLDisplay"}]}, "title": "Stackpanel Children", "type": "array"}}, "required": ["stackpanel_element_id", "stackpanel_coordinates", "stackpanel_orientation"], "title": "StackPanelContainer", "type": "object"}, "StackPanelOrientation": {"enum": ["horizontal", "vertical"], "title": "StackPanelOrientation", "type": "string"}, "StyleDefinition": {"properties": {"style_id": {"description": "Style reference identifier", "title": "Style Id", "type": "string"}, "style_font": {"anyOf": [{"$ref": "#/$defs/FontStyleDef"}, {"type": "null"}], "default": null}, "style_text": {"anyOf": [{"$ref": "#/$defs/TextStyleDef"}, {"type": "null"}], "default": null}, "style_border": {"anyOf": [{"$ref": "#/$defs/BorderStyleDef"}, {"type": "null"}], "default": null}, "style_background": {"anyOf": [{"$ref": "#/$defs/BackgroundStyleDef"}, {"type": "null"}], "default": null}, "style_alignment": {"anyOf": [{"$ref": "#/$defs/AlignmentStyleDef"}, {"type": "null"}], "default": null}, "style_animation": {"anyOf": [{"$ref": "#/$defs/AnimationStyleDef"}, {"type": "null"}], "default": null}}, "required": ["style_id"], "title": "StyleDefinition", "type": "object"}, "TextBoxType": {"enum": ["singleline", "multiline"], "title": "TextBoxType", "type": "string"}, "TextCasing": {"enum": ["lower", "normal", "upper"], "title": "TextCasing", "type": "string"}, "TextDisplay": {"description": "Static text display - no inheritance", "properties": {"textdisplay_element_id": {"description": "Unique element identifier", "title": "Textdisplay Element Id", "type": "string"}, "textdisplay_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Textdisplay Style Ref"}, "textdisplay_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Textdisplay Visible"}, "textdisplay_coordinates": {"$ref": "#/$defs/Coordinates"}, "textdisplay_type": {"$ref": "#/$defs/TextBoxType", "default": "singleline"}, "textdisplay_children": {"items": {"anyOf": [{"$ref": "#/$defs/TextSpan"}, {"$ref": "#/$defs/TextRun"}]}, "title": "Textdisplay Children", "type": "array"}}, "required": ["textdisplay_element_id", "textdisplay_coordinates"], "title": "TextDisplay", "type": "object"}, "TextInput": {"description": "Text input field - all properties explicit, no inheritance", "properties": {"textinput_element_id": {"description": "Unique element identifier", "title": "Textinput Element Id", "type": "string"}, "textinput_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Textinput Style Ref"}, "textinput_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Textinput Visible"}, "textinput_coordinates": {"$ref": "#/$defs/Coordinates"}, "textinput_type": {"$ref": "#/$defs/TextBoxType", "default": "singleline"}, "textinput_content": {"description": "Text that can contain bindings", "title": "Textinput Content", "type": "string"}}, "required": ["textinput_element_id", "textinput_coordinates", "textinput_content"], "title": "TextInput", "type": "object"}, "TextRun": {"description": "Inline text with styling - no inheritance", "properties": {"textrun_element_id": {"description": "Unique element identifier", "title": "Textrun Element Id", "type": "string"}, "textrun_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Textrun Style Ref"}, "textrun_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Textrun Visible"}, "textrun_content": {"description": "Text that can contain bindings", "title": "Textrun Content", "type": "string"}}, "required": ["textrun_element_id", "textrun_content"], "title": "TextRun", "type": "object"}, "TextSpan": {"description": "Container for text runs - no inheritance", "properties": {"textspan_element_id": {"description": "Unique element identifier", "title": "Textspan Element Id", "type": "string"}, "textspan_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Textspan Style Ref"}, "textspan_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Textspan Visible"}, "textspan_children": {"items": {"anyOf": [{"$ref": "#/$defs/TextSpan"}, {"$ref": "#/$defs/TextRun"}]}, "title": "Textspan Children", "type": "array"}}, "required": ["textspan_element_id"], "title": "TextSpan", "type": "object"}, "TextStyleDef": {"properties": {"text_casing": {"anyOf": [{"$ref": "#/$defs/TextCasing"}, {"type": "null"}], "default": null}}, "title": "TextStyleDef", "type": "object"}, "VerticalAlignment": {"enum": ["top", "center", "bottom"], "title": "VerticalAlignment", "type": "string"}, "VideoDisplay": {"description": "Video display element - no inheritance", "properties": {"video_element_id": {"description": "Unique element identifier", "title": "Video Element Id", "type": "string"}, "video_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Video Style Ref"}, "video_visible": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": true, "description": "Boolean that can contain bindings", "title": "Video Visible"}, "video_coordinates": {"$ref": "#/$defs/Coordinates"}, "video_source": {"description": "Text that can contain bindings", "title": "Video Source", "type": "string"}, "video_loop": {"anyOf": [{"type": "boolean"}, {"type": "string"}], "default": false, "description": "Boolean that can contain bindings", "title": "Video Loop"}}, "required": ["video_element_id", "video_coordinates", "video_source"], "title": "VideoDisplay", "type": "object"}, "ViewContainer": {"description": "Top-level view container - no inheritance", "properties": {"view_element_id": {"description": "Unique element identifier", "title": "View Element Id", "type": "string"}, "view_style_ref": {"anyOf": [{"description": "Style reference identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "View Style Ref"}, "view_coordinates": {"$ref": "#/$defs/Coordinates"}, "view_is_default": {"default": false, "title": "View Is Default", "type": "boolean"}, "view_home_timeout": {"anyOf": [{"maximum": 65535, "minimum": 0, "type": "integer"}, {"type": "null"}], "default": null, "title": "View Home Timeout"}, "view_children": {"items": {"anyOf": [{"$ref": "#/$defs/PanelContainer"}, {"$ref": "#/$defs/StackPanelContainer"}, {"$ref": "#/$defs/TextInput"}, {"$ref": "#/$defs/TextDisplay"}, {"$ref": "#/$defs/ImageDisplay"}, {"$ref": "#/$defs/VideoDisplay"}, {"$ref": "#/$defs/HTMLDisplay"}, {"$ref": "#/$defs/ButtonElement"}]}, "title": "View Children", "type": "array"}, "view_visible": {"const": true, "default": true, "enum": [true], "title": "View Visible", "type": "boolean"}}, "required": ["view_element_id", "view_coordinates"], "title": "ViewContainer", "type": "object"}, "ClearContentCommand": {"properties": {"clear_target": {"anyOf": [{"description": "Unique element identifier", "type": "string"}, {"type": "null"}], "default": null, "title": "Clear Target"}}, "title": "ClearContentCommand", "type": "object"}, "InlineStyleContent": {"properties": {"inline_style_id": {"description": "Style reference identifier", "title": "Inline Style Id", "type": "string"}, "inline_style_content": {"title": "Inline Style Content", "type": "string"}, "inline_nested_styles": {"items": {"$ref": "#/$defs/InlineStyleContent"}, "title": "Inline Nested Styles", "type": "array"}}, "required": ["inline_style_id", "inline_style_content"], "title": "InlineStyleContent", "type": "object"}, "ListItem": {"properties": {"listitem_content": {"title": "Listitem Content", "type": "string"}, "listitem_styles": {"items": {"$ref": "#/$defs/InlineStyleContent"}, "title": "Listitem Styles", "type": "array"}}, "required": ["listitem_content"], "title": "ListItem", "type": "object"}, "SetListCommand": {"properties": {"setlist_name": {"title": "Setlist Name", "type": "string"}, "setlist_items": {"items": {"$ref": "#/$defs/ListItem"}, "title": "Setlist Items", "type": "array"}}, "required": ["setlist_name"], "title": "SetListCommand", "type": "object"}, "SetVariableCommand": {"properties": {"variable_name": {"title": "Variable Name", "type": "string"}, "variable_content": {"title": "Variable Content", "type": "string"}, "variable_styles": {"items": {"$ref": "#/$defs/InlineStyleContent"}, "title": "Variable Styles", "type": "array"}}, "required": ["variable_name", "variable_content"], "title": "SetVariableCommand", "type": "object"}, "ContentState": {"type": "object", "properties": {"content_target_form": {"title": "Content Target Form", "type": "string"}, "content_target_view": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content Target View"}, "content_commands": {"items": {"anyOf": [{"$ref": "#/$defs/SetVariableCommand"}, {"$ref": "#/$defs/SetListCommand"}, {"$ref": "#/$defs/ClearContentCommand"}]}, "title": "Content Commands", "type": "array"}}, "required": ["content_target_form"], "title": "ContentState", "description": "Variable state container for CSDIP protocol"}}}