/* eslint-disable */
/**
 * This file was automatically generated by json-schema-to-typescript.
 * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
 * and run json-schema-to-typescript to regenerate this file.
 */

/**
 * Style reference identifier
 */
export type StyleId = string;
export type FontColor = string | null;
export type FontFamily = string | null;
export type FontSize = number | null;
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "FontWeight".
 */
export type FontWeight =
  | "100"
  | "200"
  | "300"
  | "400"
  | "500"
  | "600"
  | "700"
  | "800"
  | "900"
  | "light"
  | "normal"
  | "bold";
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "FontStyle".
 */
export type FontStyle = "normal" | "italic";
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "FontDecoration".
 */
export type FontDecoration = "none" | "underline" | "strikeout";
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextCasing".
 */
export type TextCasing = "lower" | "normal" | "upper";
export type BorderColor = string | null;
export type BorderWidth = number | null;
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "BorderStyleType".
 */
export type BorderStyleType = "none" | "solid" | "dotted" | "dashed";
export type BackgroundColor = string | null;
export type BackgroundImage = string | null;
export type BackgroundNegative = boolean | null;
export type BackgroundTransparent = boolean | null;
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "HorizontalAlignment".
 */
export type HorizontalAlignment = "left" | "center" | "right";
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "VerticalAlignment".
 */
export type VerticalAlignment = "top" | "center" | "bottom";
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "AnimationType".
 */
export type AnimationType = "scroll" | "vscroll" | "auto-scroll" | "auto-vscroll" | "blink" | "flip" | "flip bindings";
export type AnimationSpeed = number;
export type AnimationDuration = number | null;
export type AnimationOffset = number | null;
export type AnimationGap = number | null;
export type LayoutStyles = StyleDefinition[];
/**
 * Unique element identifier
 */
export type FormElementId = string;
export type FormStyleRef = string | null;
/**
 * @minItems 1
 */
export type FormViews = [ViewContainer, ...ViewContainer[]];
/**
 * Unique element identifier
 */
export type ViewElementId = string;
export type ViewStyleRef = string | null;
/**
 * Pixel value as unsigned short
 */
export type Top = number;
/**
 * Pixel value as unsigned short
 */
export type Left = number;
/**
 * Pixel value as unsigned short
 */
export type Width = number;
/**
 * Pixel value as unsigned short
 */
export type Height = number;
export type ViewIsDefault = boolean;
export type ViewHomeTimeout = number | null;
/**
 * Unique element identifier
 */
export type PanelElementId = string;
export type PanelStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type PanelVisible = boolean | string;
/**
 * Unique element identifier
 */
export type StackpanelElementId = string;
export type StackpanelStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type StackpanelVisible = boolean | string;
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "StackPanelOrientation".
 */
export type StackPanelOrientation = "horizontal" | "vertical";
/**
 * Unique element identifier
 */
export type TextinputElementId = string;
export type TextinputStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type TextinputVisible = boolean | string;
export type TextBoxType = "singleline" | "multiline";
/**
 * Text that can contain bindings
 */
export type TextinputContent = string;
/**
 * Unique element identifier
 */
export type TextdisplayElementId = string;
export type TextdisplayStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type TextdisplayVisible = boolean | string;
export type TextBoxType1 = "singleline" | "multiline";
/**
 * Unique element identifier
 */
export type TextspanElementId = string;
export type TextspanStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type TextspanVisible = boolean | string;
/**
 * Unique element identifier
 */
export type TextrunElementId = string;
export type TextrunStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type TextrunVisible = boolean | string;
/**
 * Text that can contain bindings
 */
export type TextrunContent = string;
export type TextspanChildren = (TextSpan | TextRun)[];
export type TextdisplayChildren = (TextSpan | TextRun)[];
/**
 * Unique element identifier
 */
export type ImageElementId = string;
export type ImageStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type ImageVisible = boolean | string;
/**
 * Text that can contain bindings
 */
export type ImageSource = string;
/**
 * Unique element identifier
 */
export type VideoElementId = string;
export type VideoStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type VideoVisible = boolean | string;
/**
 * Text that can contain bindings
 */
export type VideoSource = string;
/**
 * Boolean that can contain bindings
 */
export type VideoLoop = boolean | string;
/**
 * Unique element identifier
 */
export type HtmlElementId = string;
export type HtmlStyleRef = string | null;
/**
 * Boolean that can contain bindings
 */
export type HtmlVisible = boolean | string;
/**
 * Text that can contain bindings
 */
export type HtmlSource = string;
export type HtmlCommandName = string;
export type HtmlCommandName1 = string;
export type HtmlCommands = (ScrollCommand | ExecuteScriptCommand)[];
export type StackpanelChildren = (
  | PanelContainer
  | StackPanelContainer
  | TextInput
  | TextDisplay
  | ImageDisplay
  | VideoDisplay
  | HTMLDisplay
)[];
export type PanelChildren = (
  | PanelContainer
  | StackPanelContainer
  | TextInput
  | TextDisplay
  | ImageDisplay
  | VideoDisplay
  | HTMLDisplay
)[];
/**
 * Unique element identifier
 */
export type ButtonElementId = string;
export type ButtonNumber = number;
export type ButtonCommand = string;
export type ButtonTarget = string | null;
export type ButtonParamName = string;
export type ButtonParamValue = string;
export type ButtonParams = ButtonParameter[];
export type ViewChildren = (
  | PanelContainer
  | StackPanelContainer
  | TextInput
  | TextDisplay
  | ImageDisplay
  | VideoDisplay
  | HTMLDisplay
  | ButtonElement
)[];
export type ViewVisible = true;
export type LayoutForms = FormDefinition[];
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextBoxType".
 */
export type TextBoxType2 = "singleline" | "multiline";
export type ClearTarget = string | null;
/**
 * Style reference identifier
 */
export type InlineStyleId = string;
export type InlineStyleContent1 = string;
export type InlineNestedStyles = InlineStyleContent[];
export type ListitemContent = string;
export type ListitemStyles = InlineStyleContent[];
export type SetlistName = string;
export type SetlistItems = ListItem[];
export type VariableName = string;
export type VariableContent = string;
export type VariableStyles = InlineStyleContent[];
export type ContentTargetForm = string;
export type ContentTargetView = string | null;
export type ContentCommands = (SetVariableCommand | SetListCommand | ClearContentCommand)[];

/**
 * Unified type definitions for CSDIP display protocol with semantic naming
 */
export interface CSDIPDisplayProtocolUnifiedTypes {
  layout_styles?: LayoutStyles;
  layout_forms?: LayoutForms;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "StyleDefinition".
 */
export interface StyleDefinition {
  style_id: StyleId;
  style_font?: FontStyleDef | null;
  style_text?: TextStyleDef | null;
  style_border?: BorderStyleDef | null;
  style_background?: BackgroundStyleDef | null;
  style_alignment?: AlignmentStyleDef | null;
  style_animation?: AnimationStyleDef | null;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "FontStyleDef".
 */
export interface FontStyleDef {
  font_color?: FontColor;
  font_family?: FontFamily;
  font_size?: FontSize;
  font_weight?: FontWeight | null;
  font_style?: FontStyle | null;
  font_decoration?: FontDecoration | null;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextStyleDef".
 */
export interface TextStyleDef {
  text_casing?: TextCasing | null;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "BorderStyleDef".
 */
export interface BorderStyleDef {
  border_color?: BorderColor;
  border_width?: BorderWidth;
  border_style?: BorderStyleType | null;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "BackgroundStyleDef".
 */
export interface BackgroundStyleDef {
  background_color?: BackgroundColor;
  background_image?: BackgroundImage;
  background_negative?: BackgroundNegative;
  background_transparent?: BackgroundTransparent;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "AlignmentStyleDef".
 */
export interface AlignmentStyleDef {
  align_horizontal?: HorizontalAlignment | null;
  align_vertical?: VerticalAlignment | null;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "AnimationStyleDef".
 */
export interface AnimationStyleDef {
  animation_type: AnimationType;
  animation_speed: AnimationSpeed;
  animation_duration?: AnimationDuration;
  animation_offset?: AnimationOffset;
  animation_gap?: AnimationGap;
}
/**
 * Complete form definition - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "FormDefinition".
 */
export interface FormDefinition {
  form_element_id: FormElementId;
  form_style_ref?: FormStyleRef;
  form_views: FormViews;
}
/**
 * Top-level view container - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ViewContainer".
 */
export interface ViewContainer {
  view_element_id: ViewElementId;
  view_style_ref?: ViewStyleRef;
  view_coordinates: Coordinates;
  view_is_default?: ViewIsDefault;
  view_home_timeout?: ViewHomeTimeout;
  view_children?: ViewChildren;
  view_visible?: ViewVisible;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "Coordinates".
 */
export interface Coordinates {
  top?: Top;
  left?: Left;
  width: Width;
  height: Height;
}
/**
 * Generic container panel - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "PanelContainer".
 */
export interface PanelContainer {
  panel_element_id: PanelElementId;
  panel_style_ref?: PanelStyleRef;
  panel_visible?: PanelVisible;
  panel_coordinates: Coordinates;
  panel_children?: PanelChildren;
}
/**
 * Stack layout container - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "StackPanelContainer".
 */
export interface StackPanelContainer {
  stackpanel_element_id: StackpanelElementId;
  stackpanel_style_ref?: StackpanelStyleRef;
  stackpanel_visible?: StackpanelVisible;
  stackpanel_coordinates: Coordinates;
  stackpanel_orientation: StackPanelOrientation;
  stackpanel_children?: StackpanelChildren;
}
/**
 * Text input field - all properties explicit, no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextInput".
 */
export interface TextInput {
  textinput_element_id: TextinputElementId;
  textinput_style_ref?: TextinputStyleRef;
  textinput_visible?: TextinputVisible;
  textinput_coordinates: Coordinates;
  textinput_type?: TextBoxType;
  textinput_content: TextinputContent;
}
/**
 * Static text display - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextDisplay".
 */
export interface TextDisplay {
  textdisplay_element_id: TextdisplayElementId;
  textdisplay_style_ref?: TextdisplayStyleRef;
  textdisplay_visible?: TextdisplayVisible;
  textdisplay_coordinates: Coordinates;
  textdisplay_type?: TextBoxType1;
  textdisplay_children?: TextdisplayChildren;
}
/**
 * Container for text runs - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextSpan".
 */
export interface TextSpan {
  textspan_element_id: TextspanElementId;
  textspan_style_ref?: TextspanStyleRef;
  textspan_visible?: TextspanVisible;
  textspan_children?: TextspanChildren;
}
/**
 * Inline text with styling - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "TextRun".
 */
export interface TextRun {
  textrun_element_id: TextrunElementId;
  textrun_style_ref?: TextrunStyleRef;
  textrun_visible?: TextrunVisible;
  textrun_content: TextrunContent;
}
/**
 * Image display element - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ImageDisplay".
 */
export interface ImageDisplay {
  image_element_id: ImageElementId;
  image_style_ref?: ImageStyleRef;
  image_visible?: ImageVisible;
  image_coordinates: Coordinates;
  image_source: ImageSource;
}
/**
 * Video display element - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "VideoDisplay".
 */
export interface VideoDisplay {
  video_element_id: VideoElementId;
  video_style_ref?: VideoStyleRef;
  video_visible?: VideoVisible;
  video_coordinates: Coordinates;
  video_source: VideoSource;
  video_loop?: VideoLoop;
}
/**
 * HTML content display - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "HTMLDisplay".
 */
export interface HTMLDisplay {
  html_element_id: HtmlElementId;
  html_style_ref?: HtmlStyleRef;
  html_visible?: HtmlVisible;
  html_coordinates: Coordinates;
  html_source: HtmlSource;
  html_commands?: HTMLCommands | null;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "HTMLCommands".
 */
export interface HTMLCommands {
  html_commands?: HtmlCommands;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ScrollCommand".
 */
export interface ScrollCommand {
  html_command_name: HtmlCommandName;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ExecuteScriptCommand".
 */
export interface ExecuteScriptCommand {
  html_command_name: HtmlCommandName1;
}
/**
 * Interactive button - no inheritance
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ButtonElement".
 */
export interface ButtonElement {
  button_element_id: ButtonElementId;
  button_number: ButtonNumber;
  button_command: ButtonCommand;
  button_target?: ButtonTarget;
  button_params?: ButtonParams;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ButtonParameter".
 */
export interface ButtonParameter {
  button_param_name: ButtonParamName;
  button_param_value: ButtonParamValue;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ClearContentCommand".
 */
export interface ClearContentCommand {
  clear_target?: ClearTarget;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "InlineStyleContent".
 */
export interface InlineStyleContent {
  inline_style_id: InlineStyleId;
  inline_style_content: InlineStyleContent1;
  inline_nested_styles?: InlineNestedStyles;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ListItem".
 */
export interface ListItem {
  listitem_content: ListitemContent;
  listitem_styles?: ListitemStyles;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "SetListCommand".
 */
export interface SetListCommand {
  setlist_name: SetlistName;
  setlist_items?: SetlistItems;
}
/**
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "SetVariableCommand".
 */
export interface SetVariableCommand {
  variable_name: VariableName;
  variable_content: VariableContent;
  variable_styles?: VariableStyles;
}
/**
 * Variable state container for CSDIP protocol
 *
 * This interface was referenced by `CSDIPDisplayProtocolUnifiedTypes`'s JSON-Schema
 * via the `definition` "ContentState".
 */
export interface ContentState {
  content_target_form: ContentTargetForm;
  content_target_view?: ContentTargetView;
  content_commands?: ContentCommands;
}
