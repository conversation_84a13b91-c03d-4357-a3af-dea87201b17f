#!/usr/bin/env python3
"""Simple validation test for the direct composition model"""

from csdip_protocol.renderer_model.types_unified import (
    LayoutDefinition, FormDefinition, ViewContainer, 
    TextInput, TextDisplay, ImageDisplay, VideoDisplay,
    PanelContainer, StyleDefinition, FontStyleDef,
    Coordinates, TextBoxType
)

def test_basic_model_creation():
    """Test that we can create all major model types"""
    
    # Test coordinates
    coords = Coordinates(top=10, left=20, width=100, height=50)
    assert coords.top == 10
    assert coords.left == 20
    assert coords.width == 100
    assert coords.height == 50
    
    # Test text input with direct composition
    text_input = TextInput(
        textinput_element_id="test_input",
        textinput_style_ref="style1",
        textinput_visible=True,
        textinput_coordinates=coords,
        textinput_type=TextBoxType.SINGLELINE,
        textinput_content="Test content"
    )
    assert text_input.textinput_element_id == "test_input"
    assert text_input.textinput_style_ref == "style1"
    assert text_input.textinput_visible == True
    assert text_input.textinput_type == TextBoxType.SINGLELINE
    
    # Test text display with direct composition
    text_display = TextDisplay(
        textdisplay_element_id="test_display",
        textdisplay_coordinates=coords,
        textdisplay_type=TextBoxType.MULTILINE,
        textdisplay_children=[]
    )
    assert text_display.textdisplay_element_id == "test_display"
    assert text_display.textdisplay_type == TextBoxType.MULTILINE
    
    # Test image display with direct composition
    image = ImageDisplay(
        image_element_id="test_image",
        image_coordinates=coords,
        image_source="test.jpg"
    )
    assert image.image_element_id == "test_image"
    assert image.image_source == "test.jpg"
    
    # Test panel container with direct composition
    panel = PanelContainer(
        panel_element_id="test_panel",
        panel_coordinates=coords,
        panel_children=[text_input, text_display, image]
    )
    assert panel.panel_element_id == "test_panel"
    assert len(panel.panel_children) == 3
    
    # Test view container
    view = ViewContainer(
        view_element_id="test_view",
        view_coordinates=coords,
        view_children=[panel]
    )
    assert view.view_element_id == "test_view"
    assert len(view.view_children) == 1
    
    # Test form definition
    form = FormDefinition(
        form_element_id="test_form",
        form_views=[view]
    )
    assert form.form_element_id == "test_form"
    assert len(form.form_views) == 1
    
    # Test layout definition
    layout = LayoutDefinition(
        layout_styles=[],
        layout_forms=[form]
    )
    assert len(layout.layout_forms) == 1
    
    print("✓ All basic model creation tests passed!")

def test_style_system():
    """Test that style system works with direct composition"""
    
    font_style = FontStyleDef(
        font_color="FF0000",
        font_family="Arial",
        font_size=12
    )
    assert font_style.font_color == "FF0000"
    assert font_style.font_family == "Arial"
    assert font_style.font_size == 12
    
    style = StyleDefinition(
        style_id="test_style",
        style_font=font_style
    )
    assert style.style_id == "test_style"
    assert style.style_font.font_color == "FF0000"
    
    print("✓ Style system test passed!")

def test_property_uniqueness():
    """Verify that all element types have unique property names"""
    
    # Create instances and check that property names don't conflict
    coords = Coordinates(top=0, left=0, width=100, height=100)
    
    text_input = TextInput(
        textinput_element_id="input1",
        textinput_coordinates=coords,
        textinput_content="test"
    )
    
    text_display = TextDisplay(
        textdisplay_element_id="display1", 
        textdisplay_coordinates=coords
    )
    
    image = ImageDisplay(
        image_element_id="image1",
        image_coordinates=coords,
        image_source="test.jpg"
    )
    
    video = VideoDisplay(
        video_element_id="video1",
        video_coordinates=coords,
        video_source="test.mp4"
    )
    
    # Verify each has unique property name patterns
    assert hasattr(text_input, 'textinput_element_id')
    assert hasattr(text_display, 'textdisplay_element_id')
    assert hasattr(image, 'image_element_id')
    assert hasattr(video, 'video_element_id')
    
    # Verify no inheritance conflicts
    assert not hasattr(text_input, 'element_id')  # Old inheritance property
    assert not hasattr(text_display, 'element_id')
    assert not hasattr(image, 'element_id')
    assert not hasattr(video, 'element_id')
    
    print("✓ Property uniqueness test passed!")

if __name__ == "__main__":
    test_basic_model_creation()
    test_style_system()
    test_property_uniqueness()
    print("\n🎉 All tests passed! Direct composition model is working correctly.")
