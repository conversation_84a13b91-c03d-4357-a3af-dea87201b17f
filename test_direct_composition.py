# Test: Direct composition instead of inheritance to eliminate type proliferation
#
# This version replaces inheritance with direct property composition
# to test if this eliminates the remaining numbered types

from typing import List, Optional, Union, Literal, Any, Annotated
from pydantic import BaseModel, field_validator, Field
import re
from enum import Enum

# Reuse the same enums and validation functions
class AnimationType(str, Enum):
    SCROLL = "scroll"
    VSCROLL = "vscroll"
    AUTO_SCROLL = "auto-scroll"
    AUTO_VSCROLL = "auto-vscroll"
    BLINK = "blink"
    FLIP = "flip"
    FLIP_BINDINGS = "flip bindings"

class TextBoxType(str, Enum):
    SINGLELINE = "singleline"
    MULTILINE = "multiline"

class FontWeight(str, Enum):
    W100 = "100"
    W200 = "200"
    W300 = "300"
    W400 = "400"
    W500 = "500"
    W600 = "600"
    W700 = "700"
    W800 = "800"
    W900 = "900"
    LIGHT = "light"
    NORMAL = "normal"
    BOLD = "bold"

class FontStyle(str, Enum):
    NORMAL = "normal"
    ITALIC = "italic"

class FontDecoration(str, Enum):
    NONE = "none"
    UNDERLINE = "underline"
    STRIKEOUT = "strikeout"

class TextCasing(str, Enum):
    LOWER = "lower"
    NORMAL = "normal"
    UPPER = "upper"

class HorizontalAlignment(str, Enum):
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"

class VerticalAlignment(str, Enum):
    TOP = "top"
    CENTER = "center"
    BOTTOM = "bottom"

class BorderStyleType(str, Enum):
    NONE = "none"
    SOLID = "solid"
    DOTTED = "dotted"
    DASHED = "dashed"

class StackPanelOrientation(str, Enum):
    HORIZONTAL = "horizontal"
    VERTICAL = "vertical"

class ImageFormat(str, Enum):
    BMP = "bmp"
    GIF = "gif"
    JPG = "jpg"
    PNG = "png"

# Validation functions
def validate_color(v: str) -> str:
    if not isinstance(v, str):
        raise ValueError('Color must be a string')
    if not re.match(r'^[0-9a-fA-F]{6}$', v):
        raise ValueError('Color must be in RRGGBB hexadecimal format')
    return v

def validate_pixel(v: int) -> int:
    if not isinstance(v, int) or v < 0 or v > 65535:
        raise ValueError('Pixel value must be between 0 and 65535')
    return v

def validate_font_size(v: int) -> int:
    if not isinstance(v, int) or v < 0 or v > 9999:
        raise ValueError('Font size must be between 0 and 9999')
    return v

def validate_bindable_boolean(v: Union[bool, str]) -> Union[bool, str]:
    if isinstance(v, bool):
        return v
    if isinstance(v, str):
        if re.match(r'^\{[@!].*\}$', v):
            return v
        if v.lower() in ('true', '1'):
            return True
        elif v.lower() in ('false', '0'):
            return False
    raise ValueError('Value must be boolean or binding pattern {[@!]...}')

# Shared types
CSSColor = Annotated[str, Field(description="Color in hexadecimal format RRGGBB")]
PixelValue = Annotated[int, Field(ge=0, le=65535, description="Pixel value as unsigned short")]
FontSize = Annotated[int, Field(ge=0, le=9999, description="Font size")]
ElementId = Annotated[str, Field(description="Unique element identifier")]
StyleId = Annotated[str, Field(description="Style reference identifier")]
BindableText = Annotated[str, Field(description="Text that can contain bindings")]
BindableBoolean = Annotated[Union[bool, str], Field(description="Boolean that can contain bindings")]

# Coordinate system
class Coordinates(BaseModel):
    top: PixelValue = 0
    left: PixelValue = 0
    width: PixelValue
    height: PixelValue

    @field_validator('top', 'left', 'width', 'height')
    @classmethod
    def validate_pixel_fields(cls, v):
        return validate_pixel(v)

# Style system (unchanged)
class FontStyleDef(BaseModel):
    font_color: Optional[CSSColor] = None
    font_family: Optional[str] = None
    font_size: Optional[FontSize] = None
    font_weight: Optional[FontWeight] = None
    font_style: Optional[FontStyle] = None
    font_decoration: Optional[FontDecoration] = None

    @field_validator('font_color')
    @classmethod
    def validate_color_field(cls, v):
        return validate_color(v) if v is not None else v

    @field_validator('font_size')
    @classmethod
    def validate_size_field(cls, v):
        return validate_font_size(v) if v is not None else v

class TextStyleDef(BaseModel):
    text_casing: Optional[TextCasing] = None

class BorderStyleDef(BaseModel):
    border_color: Optional[CSSColor] = None
    border_width: Optional[PixelValue] = None
    border_style: Optional[BorderStyleType] = None

    @field_validator('border_color')
    @classmethod
    def validate_color_field(cls, v):
        return validate_color(v) if v is not None else v

    @field_validator('border_width')
    @classmethod
    def validate_width_field(cls, v):
        return validate_pixel(v) if v is not None else v

class BackgroundStyleDef(BaseModel):
    background_color: Optional[CSSColor] = None
    background_image: Optional[str] = None
    background_negative: Optional[bool] = None
    background_transparent: Optional[bool] = None

    @field_validator('background_color')
    @classmethod
    def validate_color_field(cls, v):
        return validate_color(v) if v is not None else v

class AlignmentStyleDef(BaseModel):
    align_horizontal: Optional[HorizontalAlignment] = None
    align_vertical: Optional[VerticalAlignment] = None

class AnimationStyleDef(BaseModel):
    animation_type: AnimationType
    animation_speed: int = Field(..., ge=0)
    animation_duration: Optional[int] = Field(None, ge=0)
    animation_offset: Optional[int] = Field(None, ge=0)
    animation_gap: Optional[int] = Field(None, ge=0)

class StyleDefinition(BaseModel):
    style_id: StyleId
    style_font: Optional[FontStyleDef] = None
    style_text: Optional[TextStyleDef] = None
    style_border: Optional[BorderStyleDef] = None
    style_background: Optional[BackgroundStyleDef] = None
    style_alignment: Optional[AlignmentStyleDef] = None
    style_animation: Optional[AnimationStyleDef] = None

# DIRECT COMPOSITION: Each element type has its own unique property names
# No inheritance - all properties are explicit and uniquely named

class TextInput(BaseModel):
    """Text input field - all properties explicit, no inheritance"""
    # Core identity
    textinput_element_id: ElementId
    
    # Optional styling
    textinput_style_ref: Optional[StyleId] = None
    
    # Visibility
    textinput_visible: BindableBoolean = True
    
    # Position and size
    textinput_coordinates: Coordinates
    
    # Text input specific
    textinput_type: TextBoxType = TextBoxType.SINGLELINE
    textinput_content: BindableText

    @field_validator('textinput_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class TextRun(BaseModel):
    """Inline text with styling - no inheritance"""
    # Core identity
    textrun_element_id: ElementId
    
    # Optional styling
    textrun_style_ref: Optional[StyleId] = None
    
    # Visibility
    textrun_visible: BindableBoolean = True
    
    # Text run specific
    textrun_content: BindableText

    @field_validator('textrun_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class TextSpan(BaseModel):
    """Container for text runs - no inheritance"""
    # Core identity
    textspan_element_id: ElementId
    
    # Optional styling
    textspan_style_ref: Optional[StyleId] = None
    
    # Visibility
    textspan_visible: BindableBoolean = True
    
    # Text span specific
    textspan_children: List[Union['TextSpan', 'TextRun']] = Field(default_factory=list)

    @field_validator('textspan_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class TextDisplay(BaseModel):
    """Static text display - no inheritance"""
    # Core identity
    textdisplay_element_id: ElementId
    
    # Optional styling
    textdisplay_style_ref: Optional[StyleId] = None
    
    # Visibility
    textdisplay_visible: BindableBoolean = True
    
    # Position and size
    textdisplay_coordinates: Coordinates
    
    # Text display specific
    textdisplay_type: TextBoxType = TextBoxType.SINGLELINE
    textdisplay_children: List[Union[TextSpan, TextRun]] = Field(default_factory=list)

    @field_validator('textdisplay_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class ImageDisplay(BaseModel):
    """Image display element - no inheritance"""
    # Core identity
    image_element_id: ElementId
    
    # Optional styling
    image_style_ref: Optional[StyleId] = None
    
    # Visibility
    image_visible: BindableBoolean = True
    
    # Position and size
    image_coordinates: Coordinates
    
    # Image specific
    image_source: BindableText

    @field_validator('image_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class VideoDisplay(BaseModel):
    """Video display element - no inheritance"""
    # Core identity
    video_element_id: ElementId
    
    # Optional styling
    video_style_ref: Optional[StyleId] = None
    
    # Visibility
    video_visible: BindableBoolean = True
    
    # Position and size
    video_coordinates: Coordinates
    
    # Video specific
    video_source: BindableText
    video_loop: BindableBoolean = False

    @field_validator('video_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class HTMLCommand(BaseModel):
    html_command_name: str

class ScrollCommand(HTMLCommand):
    pass

class ExecuteScriptCommand(HTMLCommand):
    pass

class HTMLCommands(BaseModel):
    html_commands: List[Union[ScrollCommand, ExecuteScriptCommand]] = Field(default_factory=list)

class HTMLDisplay(BaseModel):
    """HTML content display - no inheritance"""
    # Core identity
    html_element_id: ElementId
    
    # Optional styling
    html_style_ref: Optional[StyleId] = None
    
    # Visibility
    html_visible: BindableBoolean = True
    
    # Position and size
    html_coordinates: Coordinates
    
    # HTML specific
    html_source: BindableText
    html_commands: Optional[HTMLCommands] = None

    @field_validator('html_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class ButtonParameter(BaseModel):
    button_param_name: str
    button_param_value: str

class ButtonElement(BaseModel):
    """Interactive button - no inheritance"""
    # Core identity (buttons are invisible but still need ID)
    button_element_id: ElementId
    
    # Button specific (no visibility, style, or coordinates)
    button_number: int = Field(..., ge=0, le=255)
    button_command: str
    button_target: Optional[str] = None
    button_params: List[ButtonParameter] = Field(default_factory=list)

class PanelContainer(BaseModel):
    """Generic container panel - no inheritance"""
    # Core identity
    panel_element_id: ElementId
    
    # Optional styling
    panel_style_ref: Optional[StyleId] = None
    
    # Visibility
    panel_visible: BindableBoolean = True
    
    # Position and size
    panel_coordinates: Coordinates
    
    # Panel specific
    panel_children: List[Union['PanelContainer', 'StackPanelContainer', TextInput, TextDisplay, ImageDisplay, VideoDisplay, HTMLDisplay]] = Field(default_factory=list)

    @field_validator('panel_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class StackPanelContainer(BaseModel):
    """Stack layout container - no inheritance"""
    # Core identity
    stackpanel_element_id: ElementId
    
    # Optional styling
    stackpanel_style_ref: Optional[StyleId] = None
    
    # Visibility
    stackpanel_visible: BindableBoolean = True
    
    # Position and size
    stackpanel_coordinates: Coordinates
    
    # Stack panel specific
    stackpanel_orientation: StackPanelOrientation
    stackpanel_children: List[Union[PanelContainer, 'StackPanelContainer', TextInput, TextDisplay, ImageDisplay, VideoDisplay, HTMLDisplay]] = Field(default_factory=list)

    @field_validator('stackpanel_visible')
    @classmethod
    def validate_visible_field(cls, v):
        return validate_bindable_boolean(v)

class ViewContainer(BaseModel):
    """Top-level view container - no inheritance"""
    # Core identity
    view_element_id: ElementId
    
    # Optional styling
    view_style_ref: Optional[StyleId] = None
    
    # Position and size
    view_coordinates: Coordinates
    
    # View specific
    view_is_default: bool = False
    view_home_timeout: Optional[int] = Field(None, ge=0, le=65535)
    view_children: List[Union[PanelContainer, StackPanelContainer, TextInput, TextDisplay, ImageDisplay, VideoDisplay, HTMLDisplay, ButtonElement]] = Field(default_factory=list)
    
    # Views are always visible
    view_visible: Literal[True] = True

class FormDefinition(BaseModel):
    """Complete form definition - no inheritance"""
    # Core identity
    form_element_id: ElementId
    
    # Optional styling
    form_style_ref: Optional[StyleId] = None
    
    # Form specific
    form_views: List[ViewContainer] = Field(..., min_items=1)

# Content model (unchanged as it doesn't use the problematic inheritance)
class InlineStyleContent(BaseModel):
    inline_style_id: StyleId
    inline_style_content: str
    inline_nested_styles: List['InlineStyleContent'] = Field(default_factory=list)

class SetVariableCommand(BaseModel):
    variable_name: str
    variable_content: str
    variable_styles: List[InlineStyleContent] = Field(default_factory=list)

class ListItem(BaseModel):
    listitem_content: str
    listitem_styles: List[InlineStyleContent] = Field(default_factory=list)

class SetListCommand(BaseModel):
    setlist_name: str
    setlist_items: List[ListItem] = Field(default_factory=list)

class ClearContentCommand(BaseModel):
    clear_target: Optional[ElementId] = None

class ContentState(BaseModel):
    content_target_form: str
    content_target_view: Optional[str] = None
    content_commands: List[Union[SetVariableCommand, SetListCommand, ClearContentCommand]] = Field(default_factory=list)

class LayoutDefinition(BaseModel):
    layout_styles: List[StyleDefinition] = Field(default_factory=list)
    layout_forms: List[FormDefinition] = Field(default_factory=list)

# Update forward references
TextSpan.model_rebuild()
PanelContainer.model_rebuild()
StackPanelContainer.model_rebuild()
InlineStyleContent.model_rebuild()
