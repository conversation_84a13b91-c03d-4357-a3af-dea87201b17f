<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="PKP-PLK/CSDIP/Devices/Display" xmlns="PKP-PLK/CSDIP/Devices/Display"
    elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="Common.xsd" />
    <xs:complexType name="AckDef" />
    <xs:complexType name="WarningDef">
        <xs:sequence>
            <xs:choice minOccurs="1" maxOccurs="unbounded">
                <xs:element name="StackPanelOverflow">
                    <xs:complexType>
                        <xs:attribute name="formID" type="xs:string" use="required" />
                        <xs:attribute name="elementID" type="xs:string" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="TextOverflow">
                    <xs:complexType>
                        <xs:attribute name="formID" type="xs:string" use="required" />
                        <xs:attribute name="elementID" type="xs:string" use="required" />
                        <xs:attribute name="text" type="xs:string" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="ImageOverflow">
                    <xs:complexType>
                        <xs:attribute name="formID" type="xs:string" use="required" />
                        <xs:attribute name="elementID" type="xs:string" use="required" />
                        <xs:attribute name="source" type="xs:string" use="required" />
                    </xs:complexType>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FaultDef">
        <xs:attribute name="type" type="FaultTypeDef" use="required" />
        <xs:attribute name="code" type="xs:string" use="optional" />
        <xs:attribute name="message" type="xs:string" use="optional" />
    </xs:complexType>
    <xs:simpleType name="FaultTypeDef">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ProtocolVersionMismatch" />
            <xs:enumeration value="XmlNotWellFormed" />
            <xs:enumeration value="XmlNotValid" />
            <xs:enumeration value="Restarting" />
            <xs:enumeration value="HardwareError" />
            <xs:enumeration value="NotSupported" />
            <xs:enumeration value="OutOfMemory" />
            <xs:enumeration value="MissingResource" />
            <xs:enumeration value="ResourceInUse" />
            <xs:enumeration value="UnknownError" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DescriptionDef">
        <xs:sequence>
            <xs:element name="Manufacturer" type="xs:string" />
            <xs:element name="UnitModel" type="xs:string" minOccurs="0" />
            <xs:element name="Model" type="xs:string" />
            <xs:element name="Type" type="xs:string" />
            <xs:element name="HorizontalResolution" type="xs:unsignedInt" />
            <xs:element name="VerticalResolution" type="xs:unsignedInt" />
            <xs:element name="ColorDepth" type="xs:unsignedInt" />
            <xs:element name="UnitID" type="xs:string" minOccurs="0" />
            <xs:element name="SN" type="xs:string" />
            <xs:element name="OS" type="xs:string" />
            <xs:element name="Firmware" type="xs:string" />
            <xs:element name="Browser" type="xs:string" />
            <xs:element name="Protocol" type="xs:string" />
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StateDef">
        <xs:attribute name="status" type="DeviceStatusDef" use="required" />
        <xs:attribute name="statusDesc" type="xs:string" use="optional" />
        <xs:attribute name="turnedOff" type="xs:boolean" use="required" />
    </xs:complexType>
    <xs:simpleType name="DeviceStatusDef">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OK" />
            <xs:enumeration value="Warning" />
            <xs:enumeration value="Failure" />
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ParamsDef">
        <xs:sequence>
            <xs:element name="Param" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="name" type="xs:string" use="required" />
                    <xs:attribute name="value" type="xs:string" use="required" />
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ConfigDef">
        <xs:sequence>
            <xs:element name="Param" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="name" type="xs:string" use="required" />
                    <xs:attribute name="value" type="xs:string" use="required" />
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CurrentPrivateKeyDef">
        <xs:simpleContent>
            <xs:extension base="xs:base64Binary">
                <xs:attribute name="generated" type="xs:boolean" use="required" />
                <xs:attribute name="modified" type="xs:dateTime" use="required" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="NextPrivateKeyDef">
        <xs:simpleContent>
            <xs:extension base="xs:base64Binary">
                <xs:attribute name="generated" type="xs:boolean" use="required" />
                <xs:attribute name="modified" type="xs:dateTime" use="required" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="DeviceCertificateDef">
        <xs:complexContent>
            <xs:extension base="DeviceCertificateChainElementDef">
                <xs:attribute name="modified" type="xs:dateTime" use="required" />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="DeviceCertificateChainElementDef">
        <xs:sequence>
            <xs:element name="IssuerCertificate" type="DeviceCertificateChainElementDef"
                minOccurs="0" />
        </xs:sequence>
        <xs:attribute name="serialNumber" type="xs:hexBinary" use="required" />
        <xs:attribute name="validFrom" type="xs:dateTime" use="required" />
        <xs:attribute name="validTo" type="xs:dateTime" use="required" />
        <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
    </xs:complexType>
    <xs:complexType name="ClientCertificatesDef">
        <xs:sequence>
            <xs:element name="Certificate" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="id" type="xs:string" use="required" />
                    <xs:attribute name="serialNumber" type="xs:hexBinary" use="required" />
                    <xs:attribute name="validFrom" type="xs:dateTime" use="required" />
                    <xs:attribute name="validTo" type="xs:dateTime" use="required" />
                    <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
                    <xs:attribute name="modified" type="xs:dateTime" use="required" />
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="MediaDef">
        <xs:sequence>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="Image" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="size" type="xs:unsignedInt" use="required" />
                        <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="Video" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="size" type="xs:unsignedInt" use="required" />
                        <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="HTML" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="size" type="xs:unsignedInt" use="required" />
                        <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="CSS">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="size" type="xs:unsignedInt" use="required" />
                        <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="Fonts">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="size" type="xs:unsignedInt" use="required" />
                        <xs:attribute name="sha1" type="xs:hexBinary" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LayoutDef">
        <xs:sequence>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="Style">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
                <xs:element name="Form">
                    <xs:complexType>
                        <xs:attribute name="id" type="xs:string" use="required" />
                        <xs:attribute name="modified" type="xs:dateTime" use="required" />
                    </xs:complexType>
                </xs:element>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ScreenDef">
        <xs:simpleContent>
            <xs:extension base="xs:base64Binary">
                <xs:attribute name="formID" type="xs:string" use="optional" />
                <xs:attribute name="viewID" type="xs:string" use="optional" />
                <xs:attribute name="format" type="ImageFormatDef" use="required" />
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="StartSubscriptionDef">
        <xs:sequence>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="ButtonPressed" type="ButtonPressedDef" />
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ButtonPressedDef">
        <xs:sequence>
            <xs:element name="Param" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="name" type="xs:string" use="required" />
                    <xs:attribute name="value" type="xs:string" use="required" />
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="eventTime" type="xs:dateTime" use="required" />
        <xs:attribute name="buttonId" type="xs:string" use="required" />
    </xs:complexType>
    <xs:complexType name="TestResultDef">
        <xs:sequence>
            <xs:element name="TestCase" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="name" type="xs:string" use="required" />
                    <xs:attribute name="result" type="TestCaseResultDef" use="required" />
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TestCaseResultDef">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Passed" />
            <xs:enumeration value="Failed" />
        </xs:restriction>
    </xs:simpleType>
</xs:schema>