<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="PKP-PLK/CSDIP/Devices/Display" xmlns="PKP-PLK/CSDIP/Devices/Display"
    elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="Device.xsd" />
    <xs:element name="Response">
        <xs:complexType>
            <xs:choice>
                <xs:element name="Ack" type="AckDef" />
                <xs:element name="Warning" type="WarningDef" />
                <xs:element name="Fault" type="FaultDef" />
                <xs:element name="Description" type="DescriptionDef" />
                <xs:element name="State" type="StateDef" />
                <xs:element name="Params" type="ParamsDef" />
                <xs:element name="Config" type="ConfigDef" />
                <xs:element name="CurrentPrivateKey" type="CurrentPrivateKeyDef" />
                <xs:element name="NextPrivateKey" type="NextPrivateKeyDef" />
                <xs:element name="DeviceCertificate" type="DeviceCertificateDef" />
                <xs:element name="ClientCertificates" type="ClientCertificatesDef" />
                <xs:element name="Media" type="MediaDef" />
                <xs:element name="Layout" type="LayoutDef" />
                <xs:element name="Screen" type="ScreenDef" />
                <xs:element name="TestResult" type="TestResultDef" />
                <xs:element name="StartSubscription" type="StartSubscriptionDef" />
            </xs:choice>
            <xs:attribute name="sent" type="xs:dateTime" use="required" />
            <xs:attribute name="version" type="xs:string" use="required" />
        </xs:complexType>
    </xs:element>
</xs:schema>