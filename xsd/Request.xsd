<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="PKP-PLK/CSDIP/Devices/Display" xmlns="PKP-PLK/CSDIP/Devices/Display"
    elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="Client.xsd" />
    <xs:element name="Request">
        <xs:complexType>
            <xs:choice>
                <xs:element name="GetDescription" type="GetDescriptionDef" />
                <xs:element name="GetState" type="GetStateDef" />
                <xs:element name="GetParams" type="GetParamsDef" />
                <xs:element name="GetConfig" type="GetConfigDef" />
                <xs:element name="SetConfig" type="SetConfigDef" />
                <xs:element name="GetCurrentPrivateKey" type="GetCurrentPrivateKeyDef" />
                <xs:element name="GetNextPrivateKey" type="GetNextPrivateKeyDef" />
                <xs:element name="SetNextPrivateKey" type="SetNextPrivateKeyDef" />
                <xs:element name="GenerateNextPrivateKey" type="GenerateNextPrivateKeyDef" />
                <xs:element name="GetDeviceCertificate" type="GetDeviceCertificateDef" />
                <xs:element name="SetDeviceCertificate" type="SetDeviceCertificateDef" />
                <xs:element name="GetClientCertificates" type="GetClientCertificatesDef" />
                <xs:element name="AddClientCertificates" type="AddClientCertificatesDef" />
                <xs:element name="RemoveClientCertificates" type="RemoveClientCertificatesDef" />
                <xs:element name="GetMedia" type="GetMediaDef" />
                <xs:element name="AddMedia" type="AddMediaDef" />
                <xs:element name="RemoveMedia" type="RemoveMediaDef" />
                <xs:element name="GetLayout" type="GetLayoutDef" />
                <xs:element name="AddLayout" type="AddLayoutDef" />
                <xs:element name="RemoveLayout" type="RemoveLayoutDef" />
                <xs:element name="Content" type="ContentDef" />
                <xs:element name="TurnOffDisplay" type="TurnOffDisplayDef" />
                <xs:element name="TurnOnDisplay" type="TurnOnDisplayDef" />
                <xs:element name="GetScreen" type="GetScreenDef" />
                <xs:element name="Subscribe" type="SubscribeDef" />
                <xs:element name="Unsubscribe" type="UnsubscribeDef" />
                <xs:element name="RestartApp" type="RestartAppDef" />
                <xs:element name="RestartOS" type="RestartOSDef" />
                <xs:element name="Test" type="TestDef" />
            </xs:choice>
            <xs:attribute name="sent" type="xs:dateTime" use="required" />
            <xs:attribute name="version" type="xs:string" use="required" />
        </xs:complexType>
    </xs:element>
</xs:schema>