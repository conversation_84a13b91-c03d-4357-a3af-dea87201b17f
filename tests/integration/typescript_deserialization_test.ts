#!/usr/bin/env node
/**
 * TypeScript deserialization tests for CSDIP integration testing.
 * Tests JSON → TypeScript deserialization with comprehensive validation.
 */

import * as fs from 'fs';
import * as path from 'path';
import {
    CSDIPDisplayProtocolUnifiedTypes,
    LayoutDefinition,
    ContentState,
    FormDefinition,
    ViewContainer,
    StyleDefinition,
    TextInput,
    TextDisplay,
    ImageDisplay,
    VideoDisplay,
    HTMLDisplay,
    ButtonElement,
    PanelContainer,
    StackPanelContainer,
    Coordinates,
    FontStyleDef,
    BorderStyleDef,
    BackgroundStyleDef,
    AlignmentStyleDef,
    AnimationStyleDef
} from '../../frontend/src/types/csdip-unified.types';

interface TestResult {
    testName: string;
    success: boolean;
    jsonData?: any;
    typedData?: any;
    validationErrors?: string[];
    typeCheckPassed?: boolean;
    fieldCount?: number;
    jsonSize?: number;
    timestamp: string;
    error?: string;
    errorType?: string;
}

interface TestSummary {
    timestamp: string;
    totalTests: number;
    successfulTests: number;
    failedTests: number;
    typeCheckFailures: number;
    successRate: number;
    jsonSizeStats: {
        min: number;
        max: number;
        avg: number;
    };
    fieldCountStats: {
        min: number;
        max: number;
        avg: number;
    };
    testCategories: {
        [key: string]: number;
    };
}

class TypeScriptDeserializationTester {
    private testResults: TestResult[] = [];

    /**
     * Count fields in nested object structure
     */
    private countFields(data: any, count: number = 0): number {
        if (typeof data === 'object' && data !== null) {
            if (Array.isArray(data)) {
                for (const item of data) {
                    count = this.countFields(item, count);
                }
            } else {
                count += Object.keys(data).length;
                for (const value of Object.values(data)) {
                    count = this.countFields(value, count);
                }
            }
        }
        return count;
    }

    /**
     * Validate that an object conforms to expected TypeScript interface structure
     */
    private validateTypeStructure(data: any, expectedType: string): string[] {
        const errors: string[] = [];

        try {
            switch (expectedType) {
                case 'LayoutDefinition':
                    this.validateLayoutDefinition(data, errors);
                    break;
                case 'ContentState':
                    this.validateContentState(data, errors);
                    break;
                case 'FormDefinition':
                    this.validateFormDefinition(data, errors);
                    break;
                case 'ViewContainer':
                    this.validateViewContainer(data, errors);
                    break;
                case 'StyleDefinition':
                    this.validateStyleDefinition(data, errors);
                    break;
                case 'TextInput':
                    this.validateTextInput(data, errors);
                    break;
                case 'TextDisplay':
                    this.validateTextDisplay(data, errors);
                    break;
                case 'ImageDisplay':
                    this.validateImageDisplay(data, errors);
                    break;
                case 'VideoDisplay':
                    this.validateVideoDisplay(data, errors);
                    break;
                case 'HTMLDisplay':
                    this.validateHTMLDisplay(data, errors);
                    break;
                case 'ButtonElement':
                    this.validateButtonElement(data, errors);
                    break;
                case 'PanelContainer':
                    this.validatePanelContainer(data, errors);
                    break;
                case 'StackPanelContainer':
                    this.validateStackPanelContainer(data, errors);
                    break;
                default:
                    errors.push(`Unknown type: ${expectedType}`);
            }
        } catch (error) {
            errors.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
        }

        return errors;
    }

    private validateLayoutDefinition(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('LayoutDefinition must be an object');
            return;
        }

        if (data.layout_styles !== undefined) {
            if (!Array.isArray(data.layout_styles)) {
                errors.push('layout_styles must be an array');
            } else {
                data.layout_styles.forEach((style: any, index: number) => {
                    const styleErrors = this.validateTypeStructure(style, 'StyleDefinition');
                    styleErrors.forEach(err => errors.push(`layout_styles[${index}]: ${err}`));
                });
            }
        }

        if (data.layout_forms !== undefined) {
            if (!Array.isArray(data.layout_forms)) {
                errors.push('layout_forms must be an array');
            } else {
                data.layout_forms.forEach((form: any, index: number) => {
                    const formErrors = this.validateTypeStructure(form, 'FormDefinition');
                    formErrors.forEach(err => errors.push(`layout_forms[${index}]: ${err}`));
                });
            }
        }
    }

    private validateContentState(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('ContentState must be an object');
            return;
        }

        if (typeof data.content_target_form !== 'string') {
            errors.push('content_target_form must be a string');
        }

        if (data.content_target_view !== undefined && data.content_target_view !== null && typeof data.content_target_view !== 'string') {
            errors.push('content_target_view must be a string or null');
        }

        if (data.content_commands !== undefined) {
            if (!Array.isArray(data.content_commands)) {
                errors.push('content_commands must be an array');
            }
        }
    }

    private validateFormDefinition(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('FormDefinition must be an object');
            return;
        }

        if (typeof data.form_element_id !== 'string') {
            errors.push('form_element_id must be a string');
        }

        if (!Array.isArray(data.form_views) || data.form_views.length === 0) {
            errors.push('form_views must be a non-empty array');
        }
    }

    private validateViewContainer(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('ViewContainer must be an object');
            return;
        }

        if (typeof data.view_element_id !== 'string') {
            errors.push('view_element_id must be a string');
        }

        if (!data.view_coordinates || typeof data.view_coordinates !== 'object') {
            errors.push('view_coordinates must be an object');
        } else {
            this.validateCoordinates(data.view_coordinates, errors, 'view_coordinates');
        }
    }

    private validateStyleDefinition(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('StyleDefinition must be an object');
            return;
        }

        if (typeof data.style_id !== 'string') {
            errors.push('style_id must be a string');
        }
    }

    private validateTextInput(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('TextInput must be an object');
            return;
        }

        if (typeof data.textinput_element_id !== 'string') {
            errors.push('textinput_element_id must be a string');
        }

        if (typeof data.textinput_content !== 'string') {
            errors.push('textinput_content must be a string');
        }

        if (!data.textinput_coordinates || typeof data.textinput_coordinates !== 'object') {
            errors.push('textinput_coordinates must be an object');
        } else {
            this.validateCoordinates(data.textinput_coordinates, errors, 'textinput_coordinates');
        }
    }

    private validateTextDisplay(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('TextDisplay must be an object');
            return;
        }

        if (typeof data.textdisplay_element_id !== 'string') {
            errors.push('textdisplay_element_id must be a string');
        }

        if (!data.textdisplay_coordinates || typeof data.textdisplay_coordinates !== 'object') {
            errors.push('textdisplay_coordinates must be an object');
        } else {
            this.validateCoordinates(data.textdisplay_coordinates, errors, 'textdisplay_coordinates');
        }
    }

    private validateImageDisplay(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('ImageDisplay must be an object');
            return;
        }

        if (typeof data.image_element_id !== 'string') {
            errors.push('image_element_id must be a string');
        }

        if (typeof data.image_source !== 'string') {
            errors.push('image_source must be a string');
        }

        if (!data.image_coordinates || typeof data.image_coordinates !== 'object') {
            errors.push('image_coordinates must be an object');
        } else {
            this.validateCoordinates(data.image_coordinates, errors, 'image_coordinates');
        }
    }

    private validateVideoDisplay(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('VideoDisplay must be an object');
            return;
        }

        if (typeof data.video_element_id !== 'string') {
            errors.push('video_element_id must be a string');
        }

        if (typeof data.video_source !== 'string') {
            errors.push('video_source must be a string');
        }

        if (!data.video_coordinates || typeof data.video_coordinates !== 'object') {
            errors.push('video_coordinates must be an object');
        } else {
            this.validateCoordinates(data.video_coordinates, errors, 'video_coordinates');
        }
    }

    private validateHTMLDisplay(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('HTMLDisplay must be an object');
            return;
        }

        if (typeof data.html_element_id !== 'string') {
            errors.push('html_element_id must be a string');
        }

        if (typeof data.html_source !== 'string') {
            errors.push('html_source must be a string');
        }

        if (!data.html_coordinates || typeof data.html_coordinates !== 'object') {
            errors.push('html_coordinates must be an object');
        } else {
            this.validateCoordinates(data.html_coordinates, errors, 'html_coordinates');
        }
    }

    private validateButtonElement(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('ButtonElement must be an object');
            return;
        }

        if (typeof data.button_element_id !== 'string') {
            errors.push('button_element_id must be a string');
        }

        if (typeof data.button_number !== 'number' || data.button_number < 0 || data.button_number > 255) {
            errors.push('button_number must be a number between 0 and 255');
        }

        if (typeof data.button_command !== 'string') {
            errors.push('button_command must be a string');
        }
    }

    private validatePanelContainer(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('PanelContainer must be an object');
            return;
        }

        if (typeof data.panel_element_id !== 'string') {
            errors.push('panel_element_id must be a string');
        }

        if (!data.panel_coordinates || typeof data.panel_coordinates !== 'object') {
            errors.push('panel_coordinates must be an object');
        } else {
            this.validateCoordinates(data.panel_coordinates, errors, 'panel_coordinates');
        }
    }

    private validateStackPanelContainer(data: any, errors: string[]): void {
        if (typeof data !== 'object' || data === null) {
            errors.push('StackPanelContainer must be an object');
            return;
        }

        if (typeof data.stackpanel_element_id !== 'string') {
            errors.push('stackpanel_element_id must be a string');
        }

        if (!data.stackpanel_coordinates || typeof data.stackpanel_coordinates !== 'object') {
            errors.push('stackpanel_coordinates must be an object');
        } else {
            this.validateCoordinates(data.stackpanel_coordinates, errors, 'stackpanel_coordinates');
        }

        if (typeof data.stackpanel_orientation !== 'string' || 
            !['horizontal', 'vertical'].includes(data.stackpanel_orientation)) {
            errors.push('stackpanel_orientation must be "horizontal" or "vertical"');
        }
    }

    private validateCoordinates(data: any, errors: string[], fieldName: string): void {
        if (typeof data !== 'object' || data === null) {
            errors.push(`${fieldName} must be an object`);
            return;
        }

        if (typeof data.width !== 'number' || data.width < 0 || data.width > 65535) {
            errors.push(`${fieldName}.width must be a number between 0 and 65535`);
        }

        if (typeof data.height !== 'number' || data.height < 0 || data.height > 65535) {
            errors.push(`${fieldName}.height must be a number between 0 and 65535`);
        }

        if (data.top !== undefined && (typeof data.top !== 'number' || data.top < 0 || data.top > 65535)) {
            errors.push(`${fieldName}.top must be a number between 0 and 65535`);
        }

        if (data.left !== undefined && (typeof data.left !== 'number' || data.left < 0 || data.left > 65535)) {
            errors.push(`${fieldName}.left must be a number between 0 and 65535`);
        }
    }

    /**
     * Test JSON deserialization and type validation
     */
    private deserializeAndValidate(jsonData: any, expectedType: string, testName: string): TestResult {
        try {
            const jsonString = JSON.stringify(jsonData);
            const jsonSize = jsonString.length;
            const fieldCount = this.countFields(jsonData);

            // Parse JSON to ensure it's valid
            const parsedData = JSON.parse(jsonString);

            // Validate type structure
            const validationErrors = this.validateTypeStructure(parsedData, expectedType);
            const typeCheckPassed = validationErrors.length === 0;

            // Try to access properties as if they were typed (runtime check)
            let typedData: any = parsedData;

            // Perform some basic type-safe operations based on the expected type
            this.performTypeSafeOperations(typedData, expectedType);

            const result: TestResult = {
                testName,
                success: true,
                jsonData: parsedData,
                typedData,
                validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
                typeCheckPassed,
                fieldCount,
                jsonSize,
                timestamp: new Date().toISOString()
            };

            return result;

        } catch (error) {
            return {
                testName,
                success: false,
                error: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Perform type-safe operations to verify TypeScript compatibility
     */
    private performTypeSafeOperations(data: any, expectedType: string): void {
        switch (expectedType) {
            case 'LayoutDefinition':
                // Access layout properties
                if (data.layout_styles) {
                    data.layout_styles.forEach((style: any) => {
                        if (style.style_id) {
                            const id: string = style.style_id;
                        }
                    });
                }
                if (data.layout_forms) {
                    data.layout_forms.forEach((form: any) => {
                        if (form.form_element_id) {
                            const id: string = form.form_element_id;
                        }
                    });
                }
                break;

            case 'ContentState':
                // Access content state properties
                const targetForm: string = data.content_target_form;
                const targetView: string | null = data.content_target_view || null;
                if (data.content_commands) {
                    const commands: any[] = data.content_commands;
                }
                break;

            case 'TextInput':
                // Access text input properties
                const elementId: string = data.textinput_element_id;
                const content: string = data.textinput_content;
                const coords = data.textinput_coordinates;
                if (coords) {
                    const width: number = coords.width;
                    const height: number = coords.height;
                }
                break;

            case 'ViewContainer':
                // Access view container properties
                const viewId: string = data.view_element_id;
                const viewCoords = data.view_coordinates;
                if (viewCoords) {
                    const viewWidth: number = viewCoords.width;
                    const viewHeight: number = viewCoords.height;
                }
                const isDefault: boolean = data.view_is_default || false;
                break;

            // Add more cases as needed
        }
    }

    /**
     * Load test data from Python serialization results
     */
    private loadPythonTestData(filePath: string): any[] {
        try {
            const fileContent = fs.readFileSync(filePath, 'utf-8');
            const pythonResults = JSON.parse(fileContent);

            if (pythonResults.detailed_results && Array.isArray(pythonResults.detailed_results)) {
                return pythonResults.detailed_results.filter((result: any) =>
                    result.success && result.json_dict
                );
            }

            return [];
        } catch (error) {
            console.error(`Failed to load Python test data from ${filePath}:`, error);
            return [];
        }
    }

    /**
     * Test deserialization of data from Python serialization tests
     */
    public testPythonGeneratedData(pythonResultsPath: string): TestResult[] {
        const results: TestResult[] = [];
        const pythonTestData = this.loadPythonTestData(pythonResultsPath);

        console.log(`Loaded ${pythonTestData.length} test cases from Python results`);

        for (const pythonResult of pythonTestData) {
            const testName = `python_${pythonResult.test_name}`;

            // Determine expected type from test name
            let expectedType = 'LayoutDefinition'; // default
            if (pythonResult.test_name.includes('TextInput')) expectedType = 'TextInput';
            else if (pythonResult.test_name.includes('TextDisplay')) expectedType = 'TextDisplay';
            else if (pythonResult.test_name.includes('ImageDisplay')) expectedType = 'ImageDisplay';
            else if (pythonResult.test_name.includes('VideoDisplay')) expectedType = 'VideoDisplay';
            else if (pythonResult.test_name.includes('HTMLDisplay')) expectedType = 'HTMLDisplay';
            else if (pythonResult.test_name.includes('ButtonElement')) expectedType = 'ButtonElement';
            else if (pythonResult.test_name.includes('PanelContainer')) expectedType = 'PanelContainer';
            else if (pythonResult.test_name.includes('StackPanelContainer')) expectedType = 'StackPanelContainer';
            else if (pythonResult.test_name.includes('ViewContainer')) expectedType = 'ViewContainer';
            else if (pythonResult.test_name.includes('FormDefinition')) expectedType = 'FormDefinition';
            else if (pythonResult.test_name.includes('StyleDefinition')) expectedType = 'StyleDefinition';
            else if (pythonResult.test_name.includes('ContentState')) expectedType = 'ContentState';

            const result = this.deserializeAndValidate(
                pythonResult.json_dict,
                expectedType,
                testName
            );

            results.push(result);
            this.testResults.push(result);
        }

        return results;
    }

    /**
     * Test with manually created TypeScript-friendly data
     */
    public testManualTypeScriptData(): TestResult[] {
        const results: TestResult[] = [];

        // Test cases with TypeScript-friendly data
        const testCases = [
            {
                name: 'simple_layout',
                type: 'LayoutDefinition',
                data: {
                    layout_styles: [
                        {
                            style_id: 'test_style_1',
                            style_font: {
                                font_color: 'ff0000',
                                font_size: 24,
                                font_weight: 'bold'
                            }
                        }
                    ],
                    layout_forms: [
                        {
                            form_element_id: 'test_form_1',
                            form_views: [
                                {
                                    view_element_id: 'test_view_1',
                                    view_coordinates: {
                                        top: 0,
                                        left: 0,
                                        width: 1920,
                                        height: 1080
                                    },
                                    view_is_default: true,
                                    view_children: []
                                }
                            ]
                        }
                    ]
                }
            },
            {
                name: 'content_state',
                type: 'ContentState',
                data: {
                    content_target_form: 'test_form',
                    content_target_view: 'test_view',
                    content_commands: [
                        {
                            variable_name: 'station_name',
                            variable_content: 'Central Station',
                            variable_styles: []
                        }
                    ]
                }
            },
            {
                name: 'text_input',
                type: 'TextInput',
                data: {
                    textinput_element_id: 'input_1',
                    textinput_content: 'Test input content',
                    textinput_coordinates: {
                        top: 100,
                        left: 50,
                        width: 300,
                        height: 40
                    },
                    textinput_visible: true,
                    textinput_type: 'singleline'
                }
            }
        ];

        for (const testCase of testCases) {
            const result = this.deserializeAndValidate(
                testCase.data,
                testCase.type,
                `manual_${testCase.name}`
            );

            results.push(result);
            this.testResults.push(result);
        }

        return results;
    }

    /**
     * Run all TypeScript deserialization tests
     */
    public runAllTests(pythonResultsPath?: string): { summary: TestSummary; detailedResults: TestResult[] } {
        console.log('Running TypeScript deserialization tests...');

        // Test manual TypeScript data
        const manualResults = this.testManualTypeScriptData();
        console.log(`Completed ${manualResults.length} manual tests`);

        // Test Python-generated data if available
        let pythonResults: TestResult[] = [];
        if (pythonResultsPath && fs.existsSync(pythonResultsPath)) {
            pythonResults = this.testPythonGeneratedData(pythonResultsPath);
            console.log(`Completed ${pythonResults.length} Python-generated tests`);
        } else {
            console.log('Python results file not found, skipping Python-generated tests');
        }

        // Calculate summary statistics
        const allResults = this.testResults;
        const totalTests = allResults.length;
        const successfulTests = allResults.filter(r => r.success).length;
        const failedTests = totalTests - successfulTests;
        const typeCheckFailures = allResults.filter(r => r.success && !r.typeCheckPassed).length;

        const jsonSizes = allResults.filter(r => r.success && r.jsonSize).map(r => r.jsonSize!);
        const fieldCounts = allResults.filter(r => r.success && r.fieldCount).map(r => r.fieldCount!);

        const summary: TestSummary = {
            timestamp: new Date().toISOString(),
            totalTests,
            successfulTests,
            failedTests,
            typeCheckFailures,
            successRate: totalTests > 0 ? successfulTests / totalTests : 0,
            jsonSizeStats: {
                min: jsonSizes.length > 0 ? Math.min(...jsonSizes) : 0,
                max: jsonSizes.length > 0 ? Math.max(...jsonSizes) : 0,
                avg: jsonSizes.length > 0 ? jsonSizes.reduce((a, b) => a + b, 0) / jsonSizes.length : 0
            },
            fieldCountStats: {
                min: fieldCounts.length > 0 ? Math.min(...fieldCounts) : 0,
                max: fieldCounts.length > 0 ? Math.max(...fieldCounts) : 0,
                avg: fieldCounts.length > 0 ? fieldCounts.reduce((a, b) => a + b, 0) / fieldCounts.length : 0
            },
            testCategories: {
                manual: manualResults.length,
                pythonGenerated: pythonResults.length
            }
        };

        return {
            summary,
            detailedResults: allResults
        };
    }

    /**
     * Save test results to JSON file
     */
    public saveResults(results: { summary: TestSummary; detailedResults: TestResult[] }, outputPath: string): void {
        const output = {
            summary: results.summary,
            detailed_results: results.detailedResults,
            failed_tests: results.detailedResults.filter(r => !r.success),
            type_check_failures: results.detailedResults.filter(r => r.success && !r.typeCheckPassed)
        };

        fs.writeFileSync(outputPath, JSON.stringify(output, null, 2), 'utf-8');
        console.log(`TypeScript test results saved to: ${outputPath}`);
    }
}

// Main execution
if (require.main === module) {
    const tester = new TypeScriptDeserializationTester();

    // Look for Python results file
    const pythonResultsPath = path.join(__dirname, 'results', 'python_serialization_results.json');

    const results = tester.runAllTests(pythonResultsPath);

    // Print summary
    const summary = results.summary;
    console.log('\n=== TypeScript Deserialization Test Summary ===');
    console.log(`Total tests: ${summary.totalTests}`);
    console.log(`Successful: ${summary.successfulTests}`);
    console.log(`Failed: ${summary.failedTests}`);
    console.log(`Type check failures: ${summary.typeCheckFailures}`);
    console.log(`Success rate: ${(summary.successRate * 100).toFixed(2)}%`);
    console.log(`Average JSON size: ${summary.jsonSizeStats.avg.toFixed(0)} bytes`);
    console.log(`Average field count: ${summary.fieldCountStats.avg.toFixed(0)}`);

    // Save results
    const outputDir = path.join(__dirname, 'results');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    const outputPath = path.join(outputDir, 'typescript_deserialization_results.json');
    tester.saveResults(results, outputPath);
}
