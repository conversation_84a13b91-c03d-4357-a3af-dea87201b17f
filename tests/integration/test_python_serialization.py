#!/usr/bin/env python3
"""
Python serialization tests for CSDIP integration testing.
Tests Python model → JSON serialization with comprehensive validation.
"""

import json
import pytest
import sys
from pathlib import Path
from typing import Dict, Any, List, Union
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.test_data_generators import (
    TestDataGenerator, EdgeCaseGenerator, RealWorldScenarioGenerator
)
from csdip_protocol.renderer_model.types_unified import (
    LayoutDefinition, ContentState, FormDefinition, ViewContainer,
    StyleDefinition, TextInput, TextDisplay, ImageDisplay, VideoDisplay,
    HTMLDisplay, ButtonElement, PanelContainer, StackPanelContainer
)


class PythonSerializationTester:
    """Tests Python model serialization to JSON."""
    
    def __init__(self):
        self.generator = TestDataGenerator()
        self.edge_generator = EdgeCaseGenerator()
        self.scenario_generator = RealWorldScenarioGenerator()
        self.test_results = []
    
    def serialize_and_validate(self, model_instance, test_name: str) -> Dict[str, Any]:
        """Serialize model to JSON and validate the result."""
        try:
            # Serialize using Pydantic's model_dump
            python_dict = model_instance.model_dump()
            
            # Convert to JSON string and back to ensure JSON compatibility
            json_str = json.dumps(python_dict, ensure_ascii=False, indent=2)
            json_dict = json.loads(json_str)
            
            # Validate that we can recreate the model from JSON
            model_class = type(model_instance)
            recreated_model = model_class.model_validate(json_dict)
            
            # Compare original and recreated
            original_dict = model_instance.model_dump()
            recreated_dict = recreated_model.model_dump()
            
            result = {
                "test_name": test_name,
                "success": True,
                "original_dict": original_dict,
                "json_string": json_str,
                "json_dict": json_dict,
                "recreated_dict": recreated_dict,
                "data_integrity": original_dict == recreated_dict,
                "json_size": len(json_str),
                "field_count": self._count_fields(original_dict),
                "timestamp": datetime.now().isoformat()
            }
            
            if not result["data_integrity"]:
                result["differences"] = self._find_differences(original_dict, recreated_dict)
            
            return result
            
        except Exception as e:
            return {
                "test_name": test_name,
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "timestamp": datetime.now().isoformat()
            }
    
    def _count_fields(self, data: Any, count: int = 0) -> int:
        """Recursively count fields in nested data structure."""
        if isinstance(data, dict):
            count += len(data)
            for value in data.values():
                count = self._count_fields(value, count)
        elif isinstance(data, list):
            for item in data:
                count = self._count_fields(item, count)
        return count
    
    def _find_differences(self, dict1: Dict, dict2: Dict, path: str = "") -> List[str]:
        """Find differences between two dictionaries."""
        differences = []
        
        # Check keys in dict1
        for key in dict1:
            current_path = f"{path}.{key}" if path else key
            if key not in dict2:
                differences.append(f"Missing key in recreated: {current_path}")
            elif isinstance(dict1[key], dict) and isinstance(dict2[key], dict):
                differences.extend(self._find_differences(dict1[key], dict2[key], current_path))
            elif dict1[key] != dict2[key]:
                differences.append(f"Value mismatch at {current_path}: {dict1[key]} != {dict2[key]}")
        
        # Check keys in dict2 that aren't in dict1
        for key in dict2:
            if key not in dict1:
                current_path = f"{path}.{key}" if path else key
                differences.append(f"Extra key in recreated: {current_path}")
        
        return differences
    
    def test_basic_elements(self) -> List[Dict[str, Any]]:
        """Test serialization of basic elements."""
        results = []
        
        # Test individual element types
        test_cases = [
            (self.generator.generate_text_input(), "TextInput"),
            (self.generator.generate_text_display(), "TextDisplay"),
            (self.generator.generate_image_display(), "ImageDisplay"),
            (self.generator.generate_video_display(), "VideoDisplay"),
            (self.generator.generate_html_display(), "HTMLDisplay"),
            (self.generator.generate_button_element(), "ButtonElement"),
            (self.generator.generate_panel_container(), "PanelContainer"),
            (self.generator.generate_stackpanel_container(), "StackPanelContainer"),
            (self.generator.generate_view_container(), "ViewContainer"),
            (self.generator.generate_form_definition(), "FormDefinition"),
            (self.generator.generate_style_definition(), "StyleDefinition"),
            (self.generator.generate_content_state(), "ContentState")
        ]
        
        for model_instance, test_name in test_cases:
            result = self.serialize_and_validate(model_instance, f"basic_{test_name}")
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    def test_complex_layouts(self) -> List[Dict[str, Any]]:
        """Test serialization of complex layout structures."""
        results = []
        
        # Test different complexity levels
        test_cases = [
            (self.generator.generate_layout_definition(num_styles=5, num_forms=2), "SimpleLayout"),
            (self.generator.generate_layout_definition(num_styles=20, num_forms=5), "ComplexLayout"),
            (self.scenario_generator.generate_station_info_layout(), "StationInfoLayout"),
            (self.scenario_generator.generate_platform_display_content(), "PlatformContent")
        ]
        
        for model_instance, test_name in test_cases:
            result = self.serialize_and_validate(model_instance, f"complex_{test_name}")
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    def test_edge_cases(self) -> List[Dict[str, Any]]:
        """Test serialization of edge cases."""
        results = []
        
        # Test edge cases
        test_cases = [
            (self.edge_generator.generate_minimal_layout(), "MinimalLayout"),
            (self.edge_generator.generate_null_heavy_style(), "NullHeavyStyle"),
            (self.edge_generator.generate_deeply_nested_containers(), "DeeplyNested"),
            (self.edge_generator.generate_extreme_values_layout(), "ExtremeValues")
        ]
        
        # Test empty collections
        empty_layout = LayoutDefinition(layout_styles=[], layout_forms=[])
        test_cases.append((empty_layout, "EmptyLayout"))
        
        # Test boundary values
        for model_instance, test_name in test_cases:
            result = self.serialize_and_validate(model_instance, f"edge_{test_name}")
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    def test_binding_expressions(self) -> List[Dict[str, Any]]:
        """Test serialization of various binding expressions."""
        results = []

        binding_variations = self.edge_generator.generate_binding_variations()

        for i, binding in enumerate(binding_variations):
            # For textinput_visible, only use boolean bindings (starting with @ or !)
            visible_value = True
            if i % 3 == 0 and (binding.startswith('{@') or binding.startswith('{!')):
                visible_value = binding

            text_input = TextInput(
                textinput_element_id=f"binding_test_{i}",
                textinput_coordinates=self.generator.generate_coordinates(),
                textinput_content=binding,  # Content can have any binding
                textinput_visible=visible_value
            )

            result = self.serialize_and_validate(text_input, f"binding_{i:02d}_{binding.replace('{', '').replace('}', '')}")
            results.append(result)
            self.test_results.append(result)

        return results
    
    def test_special_characters(self) -> List[Dict[str, Any]]:
        """Test serialization with special characters."""
        results = []
        
        special_contents = self.edge_generator.generate_special_characters_content()
        
        for i, content in enumerate(special_contents):
            text_display = TextDisplay(
                textdisplay_element_id=f"special_chars_{i}",
                textdisplay_coordinates=self.generator.generate_coordinates(),
                textdisplay_children=[
                    self.generator.generate_text_run()._replace(textrun_content=content)
                ]
            )
            
            result = self.serialize_and_validate(text_display, f"special_chars_{i:02d}")
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all serialization tests and return summary."""
        print("Running Python serialization tests...")
        
        # Run test suites
        basic_results = self.test_basic_elements()
        complex_results = self.test_complex_layouts()
        edge_results = self.test_edge_cases()
        binding_results = self.test_binding_expressions()
        special_results = self.test_special_characters()
        
        # Calculate summary statistics
        all_results = self.test_results
        total_tests = len(all_results)
        successful_tests = len([r for r in all_results if r.get("success", False)])
        failed_tests = total_tests - successful_tests
        data_integrity_issues = len([r for r in all_results if r.get("success", False) and not r.get("data_integrity", True)])
        
        # Calculate size statistics
        json_sizes = [r.get("json_size", 0) for r in all_results if r.get("success", False)]
        field_counts = [r.get("field_count", 0) for r in all_results if r.get("success", False)]
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "data_integrity_issues": data_integrity_issues,
            "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "json_size_stats": {
                "min": min(json_sizes) if json_sizes else 0,
                "max": max(json_sizes) if json_sizes else 0,
                "avg": sum(json_sizes) / len(json_sizes) if json_sizes else 0
            },
            "field_count_stats": {
                "min": min(field_counts) if field_counts else 0,
                "max": max(field_counts) if field_counts else 0,
                "avg": sum(field_counts) / len(field_counts) if field_counts else 0
            },
            "test_categories": {
                "basic_elements": len(basic_results),
                "complex_layouts": len(complex_results),
                "edge_cases": len(edge_results),
                "binding_expressions": len(binding_results),
                "special_characters": len(special_results)
            }
        }
        
        return {
            "summary": summary,
            "detailed_results": all_results,
            "failed_tests": [r for r in all_results if not r.get("success", False)],
            "data_integrity_failures": [r for r in all_results if r.get("success", False) and not r.get("data_integrity", True)]
        }
    
    def save_results(self, results: Dict[str, Any], output_file: Path):
        """Save test results to JSON file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"Python test results saved to: {output_file}")


if __name__ == "__main__":
    tester = PythonSerializationTester()
    results = tester.run_all_tests()
    
    # Print summary
    summary = results["summary"]
    print(f"\n=== Python Serialization Test Summary ===")
    print(f"Total tests: {summary['total_tests']}")
    print(f"Successful: {summary['successful_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Data integrity issues: {summary['data_integrity_issues']}")
    print(f"Success rate: {summary['success_rate']:.2%}")
    print(f"Average JSON size: {summary['json_size_stats']['avg']:.0f} bytes")
    print(f"Average field count: {summary['field_count_stats']['avg']:.0f}")
    
    # Save results
    output_dir = Path(__file__).parent / "results"
    output_dir.mkdir(exist_ok=True)
    tester.save_results(results, output_dir / "python_serialization_results.json")
