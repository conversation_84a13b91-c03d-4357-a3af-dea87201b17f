#!/usr/bin/env python3
"""
Test data generators for CSDIP integration tests.
Creates realistic test data based on csdip_protocol.md patterns.
"""

import random
from typing import List, Dict, Any, Optional, Union
from csdip_protocol.renderer_model.types_unified import (
    # Core types
    LayoutDefinition, FormDefinition, ViewContainer, StyleDefinition,
    ContentState, SetVariableCommand, SetListCommand, ClearContentCommand,
    
    # Elements
    PanelContainer, StackPanelContainer, TextInput, TextDisplay, 
    ImageDisplay, VideoDisplay, HTMLDisplay, ButtonElement,
    TextSpan, TextRun,
    
    # Style components
    FontStyleDef, TextStyleDef, BorderStyleDef, BackgroundStyleDef,
    AlignmentStyleDef, AnimationStyleDef,
    
    # Coordinates and parameters
    Coordinates, ButtonParameter, InlineStyleContent, ListItem,
    HTMLCommands, ScrollCommand, ExecuteScriptCommand,
    
    # Enums
    AnimationType, TextBoxType, FontWeight, FontStyle, FontDecoration,
    TextCasing, HorizontalAlignment, VerticalAlignment, BorderStyleType,
    StackPanelOrientation, ImageFormat
)


class TestDataGenerator:
    """Generates realistic test data for CSDIP protocol testing."""
    
    def __init__(self, seed: int = 42):
        """Initialize with a seed for reproducible test data."""
        random.seed(seed)
        self.element_counter = 0
        self.style_counter = 0
    
    def _next_element_id(self, prefix: str = "elem") -> str:
        """Generate unique element ID."""
        self.element_counter += 1
        return f"{prefix}_{self.element_counter:04d}"
    
    def _next_style_id(self, prefix: str = "style") -> str:
        """Generate unique style ID."""
        self.style_counter += 1
        return f"{prefix}_{self.style_counter:04d}"
    
    def generate_color(self) -> str:
        """Generate valid hexadecimal color."""
        return f"{random.randint(0, 255):02x}{random.randint(0, 255):02x}{random.randint(0, 255):02x}"
    
    def generate_pixel_value(self, max_val: int = 1920) -> int:
        """Generate valid pixel value."""
        return random.randint(0, min(max_val, 65535))
    
    def generate_font_size(self) -> int:
        """Generate valid font size."""
        return random.randint(8, 72)
    
    def generate_bindable_text(self, include_bindings: bool = True) -> str:
        """Generate text that may contain bindings."""
        texts = [
            "Station Information",
            "Platform {platform_number}",
            "Next train: {train_time}",
            "Departure: {departure_time}",
            "Welcome to {station_name}",
            "Track {track_number}",
            "Service Alert: {alert_message}",
            "Temperature: {temperature}°C"
        ]
        
        if include_bindings and random.random() < 0.3:
            return random.choice(texts)
        else:
            # Return text without bindings
            return random.choice(texts).replace("{", "").replace("}", "")
    
    def generate_bindable_boolean(self) -> Union[bool, str]:
        """Generate boolean that may contain bindings."""
        if random.random() < 0.2:
            return random.choice(["{@visible}", "{!hidden}", "{@active}"])
        return random.choice([True, False])
    
    def generate_coordinates(self) -> Coordinates:
        """Generate realistic coordinates for display elements."""
        return Coordinates(
            top=self.generate_pixel_value(1080),
            left=self.generate_pixel_value(1920),
            width=self.generate_pixel_value(800),
            height=self.generate_pixel_value(600)
        )
    
    def generate_font_style(self) -> FontStyleDef:
        """Generate font style definition."""
        return FontStyleDef(
            font_color=self.generate_color() if random.random() < 0.8 else None,
            font_family=random.choice(["Arial", "Helvetica", "Times New Roman", "Courier New"]) if random.random() < 0.7 else None,
            font_size=self.generate_font_size() if random.random() < 0.9 else None,
            font_weight=random.choice(list(FontWeight)) if random.random() < 0.6 else None,
            font_style=random.choice(list(FontStyle)) if random.random() < 0.3 else None,
            font_decoration=random.choice(list(FontDecoration)) if random.random() < 0.2 else None
        )
    
    def generate_border_style(self) -> BorderStyleDef:
        """Generate border style definition."""
        return BorderStyleDef(
            border_color=self.generate_color() if random.random() < 0.6 else None,
            border_width=random.randint(1, 5) if random.random() < 0.7 else None,
            border_style=random.choice(list(BorderStyleType)) if random.random() < 0.8 else None
        )
    
    def generate_background_style(self) -> BackgroundStyleDef:
        """Generate background style definition."""
        return BackgroundStyleDef(
            background_color=self.generate_color() if random.random() < 0.8 else None,
            background_image=f"bg_{random.randint(1, 10)}.png" if random.random() < 0.3 else None,
            background_negative=random.choice([True, False]) if random.random() < 0.2 else None,
            background_transparent=random.choice([True, False]) if random.random() < 0.4 else None
        )
    
    def generate_alignment_style(self) -> AlignmentStyleDef:
        """Generate alignment style definition."""
        return AlignmentStyleDef(
            align_horizontal=random.choice(list(HorizontalAlignment)) if random.random() < 0.7 else None,
            align_vertical=random.choice(list(VerticalAlignment)) if random.random() < 0.7 else None
        )
    
    def generate_animation_style(self) -> AnimationStyleDef:
        """Generate animation style definition."""
        return AnimationStyleDef(
            animation_type=random.choice(list(AnimationType)),
            animation_speed=random.randint(10, 1000),
            animation_duration=random.randint(1000, 10000) if random.random() < 0.7 else None,
            animation_offset=random.randint(0, 100) if random.random() < 0.5 else None,
            animation_gap=random.randint(10, 50) if random.random() < 0.5 else None
        )
    
    def generate_style_definition(self) -> StyleDefinition:
        """Generate complete style definition."""
        return StyleDefinition(
            style_id=self._next_style_id(),
            style_font=self.generate_font_style() if random.random() < 0.8 else None,
            style_text=TextStyleDef(text_casing=random.choice(list(TextCasing))) if random.random() < 0.3 else None,
            style_border=self.generate_border_style() if random.random() < 0.5 else None,
            style_background=self.generate_background_style() if random.random() < 0.7 else None,
            style_alignment=self.generate_alignment_style() if random.random() < 0.6 else None,
            style_animation=self.generate_animation_style() if random.random() < 0.2 else None
        )
    
    def generate_text_input(self) -> TextInput:
        """Generate text input element."""
        return TextInput(
            textinput_element_id=self._next_element_id("textinput"),
            textinput_style_ref=self._next_style_id() if random.random() < 0.6 else None,
            textinput_visible=self.generate_bindable_boolean(),
            textinput_coordinates=self.generate_coordinates(),
            textinput_type=random.choice(list(TextBoxType)),
            textinput_content=self.generate_bindable_text()
        )
    
    def generate_text_run(self) -> TextRun:
        """Generate text run element."""
        return TextRun(
            textrun_element_id=self._next_element_id("textrun"),
            textrun_style_ref=self._next_style_id() if random.random() < 0.5 else None,
            textrun_visible=self.generate_bindable_boolean(),
            textrun_content=self.generate_bindable_text()
        )
    
    def generate_text_span(self, max_children: int = 3) -> TextSpan:
        """Generate text span with children."""
        children = []
        for _ in range(random.randint(0, max_children)):
            if random.random() < 0.7:
                children.append(self.generate_text_run())
            else:
                children.append(self.generate_text_span(max_children=1))  # Avoid deep nesting
        
        return TextSpan(
            textspan_element_id=self._next_element_id("textspan"),
            textspan_style_ref=self._next_style_id() if random.random() < 0.5 else None,
            textspan_visible=self.generate_bindable_boolean(),
            textspan_children=children
        )
    
    def generate_text_display(self) -> TextDisplay:
        """Generate text display element."""
        children = []
        for _ in range(random.randint(1, 4)):
            if random.random() < 0.6:
                children.append(self.generate_text_run())
            else:
                children.append(self.generate_text_span())
        
        return TextDisplay(
            textdisplay_element_id=self._next_element_id("textdisplay"),
            textdisplay_style_ref=self._next_style_id() if random.random() < 0.6 else None,
            textdisplay_visible=self.generate_bindable_boolean(),
            textdisplay_coordinates=self.generate_coordinates(),
            textdisplay_type=random.choice(list(TextBoxType)),
            textdisplay_children=children
        )
    
    def generate_image_display(self) -> ImageDisplay:
        """Generate image display element."""
        return ImageDisplay(
            image_element_id=self._next_element_id("image"),
            image_style_ref=self._next_style_id() if random.random() < 0.4 else None,
            image_visible=self.generate_bindable_boolean(),
            image_coordinates=self.generate_coordinates(),
            image_source=f"image_{random.randint(1, 100)}.{random.choice(['png', 'jpg', 'gif', 'bmp'])}"
        )
    
    def generate_video_display(self) -> VideoDisplay:
        """Generate video display element."""
        return VideoDisplay(
            video_element_id=self._next_element_id("video"),
            video_style_ref=self._next_style_id() if random.random() < 0.4 else None,
            video_visible=self.generate_bindable_boolean(),
            video_coordinates=self.generate_coordinates(),
            video_source=f"video_{random.randint(1, 20)}.mp4",
            video_loop=self.generate_bindable_boolean()
        )
    
    def generate_html_display(self) -> HTMLDisplay:
        """Generate HTML display element."""
        commands = None
        if random.random() < 0.3:
            html_commands = []
            for _ in range(random.randint(1, 3)):
                if random.random() < 0.5:
                    html_commands.append(ScrollCommand(html_command_name="scroll_down"))
                else:
                    html_commands.append(ExecuteScriptCommand(html_command_name="highlight_text"))
            commands = HTMLCommands(html_commands=html_commands)
        
        return HTMLDisplay(
            html_element_id=self._next_element_id("html"),
            html_style_ref=self._next_style_id() if random.random() < 0.4 else None,
            html_visible=self.generate_bindable_boolean(),
            html_coordinates=self.generate_coordinates(),
            html_source=f"content_{random.randint(1, 50)}.html",
            html_commands=commands
        )
    
    def generate_button_element(self) -> ButtonElement:
        """Generate button element."""
        params = []
        for i in range(random.randint(0, 3)):
            params.append(ButtonParameter(
                button_param_name=f"param_{i}",
                button_param_value=f"value_{i}"
            ))
        
        return ButtonElement(
            button_element_id=self._next_element_id("button"),
            button_number=random.randint(0, 255),
            button_command=random.choice(["send", "switch view", "scroll up", "scroll down"]),
            button_target=self._next_element_id("target") if random.random() < 0.6 else None,
            button_params=params
        )

    def generate_panel_container(self, max_children: int = 5, depth: int = 0) -> PanelContainer:
        """Generate panel container with children."""
        children = []
        if depth < 3:  # Limit nesting depth
            for _ in range(random.randint(0, max_children)):
                child_type = random.choice([
                    "text_input", "text_display", "image", "video", "html", "panel", "stackpanel"
                ])

                if child_type == "text_input":
                    children.append(self.generate_text_input())
                elif child_type == "text_display":
                    children.append(self.generate_text_display())
                elif child_type == "image":
                    children.append(self.generate_image_display())
                elif child_type == "video":
                    children.append(self.generate_video_display())
                elif child_type == "html":
                    children.append(self.generate_html_display())
                elif child_type == "panel" and depth < 2:
                    children.append(self.generate_panel_container(max_children=2, depth=depth+1))
                elif child_type == "stackpanel" and depth < 2:
                    children.append(self.generate_stackpanel_container(max_children=2, depth=depth+1))

        return PanelContainer(
            panel_element_id=self._next_element_id("panel"),
            panel_style_ref=self._next_style_id() if random.random() < 0.6 else None,
            panel_visible=self.generate_bindable_boolean(),
            panel_coordinates=self.generate_coordinates(),
            panel_children=children
        )

    def generate_stackpanel_container(self, max_children: int = 5, depth: int = 0) -> StackPanelContainer:
        """Generate stack panel container with children."""
        children = []
        if depth < 3:  # Limit nesting depth
            for _ in range(random.randint(0, max_children)):
                child_type = random.choice([
                    "text_input", "text_display", "image", "video", "html", "panel", "stackpanel"
                ])

                if child_type == "text_input":
                    children.append(self.generate_text_input())
                elif child_type == "text_display":
                    children.append(self.generate_text_display())
                elif child_type == "image":
                    children.append(self.generate_image_display())
                elif child_type == "video":
                    children.append(self.generate_video_display())
                elif child_type == "html":
                    children.append(self.generate_html_display())
                elif child_type == "panel" and depth < 2:
                    children.append(self.generate_panel_container(max_children=2, depth=depth+1))
                elif child_type == "stackpanel" and depth < 2:
                    children.append(self.generate_stackpanel_container(max_children=2, depth=depth+1))

        return StackPanelContainer(
            stackpanel_element_id=self._next_element_id("stackpanel"),
            stackpanel_style_ref=self._next_style_id() if random.random() < 0.6 else None,
            stackpanel_visible=self.generate_bindable_boolean(),
            stackpanel_coordinates=self.generate_coordinates(),
            stackpanel_orientation=random.choice(list(StackPanelOrientation)),
            stackpanel_children=children
        )

    def generate_view_container(self) -> ViewContainer:
        """Generate view container with realistic content."""
        children = []

        # Add some realistic elements for a railway display
        for _ in range(random.randint(3, 8)):
            element_type = random.choice([
                "text_display", "image", "panel", "stackpanel", "button", "html"
            ])

            if element_type == "text_display":
                children.append(self.generate_text_display())
            elif element_type == "image":
                children.append(self.generate_image_display())
            elif element_type == "panel":
                children.append(self.generate_panel_container(max_children=3))
            elif element_type == "stackpanel":
                children.append(self.generate_stackpanel_container(max_children=3))
            elif element_type == "button":
                children.append(self.generate_button_element())
            elif element_type == "html":
                children.append(self.generate_html_display())

        return ViewContainer(
            view_element_id=self._next_element_id("view"),
            view_style_ref=self._next_style_id() if random.random() < 0.5 else None,
            view_coordinates=Coordinates(top=0, left=0, width=1920, height=1080),  # Full screen
            view_is_default=random.random() < 0.3,  # 30% chance of being default
            view_home_timeout=random.randint(30, 600) if random.random() < 0.4 else None,
            view_children=children
        )

    def generate_form_definition(self) -> FormDefinition:
        """Generate form definition with multiple views."""
        views = []
        num_views = random.randint(1, 4)

        for i in range(num_views):
            view = self.generate_view_container()
            # Ensure first view is default if no other default exists
            if i == 0 and not any(v.view_is_default for v in views):
                view.view_is_default = True
            views.append(view)

        return FormDefinition(
            form_element_id=self._next_element_id("form"),
            form_style_ref=self._next_style_id() if random.random() < 0.3 else None,
            form_views=views
        )

    def generate_layout_definition(self, num_styles: int = 10, num_forms: int = 3) -> LayoutDefinition:
        """Generate complete layout definition."""
        styles = [self.generate_style_definition() for _ in range(num_styles)]
        forms = [self.generate_form_definition() for _ in range(num_forms)]

        return LayoutDefinition(
            layout_styles=styles,
            layout_forms=forms
        )

    def generate_inline_style_content(self, max_nested: int = 2) -> InlineStyleContent:
        """Generate inline style content with possible nesting."""
        nested_styles = []
        if max_nested > 0 and random.random() < 0.3:
            for _ in range(random.randint(1, 2)):
                nested_styles.append(self.generate_inline_style_content(max_nested - 1))

        return InlineStyleContent(
            inline_style_id=self._next_style_id("inline"),
            inline_style_content=f"color: #{self.generate_color()}; font-size: {self.generate_font_size()}px;",
            inline_nested_styles=nested_styles
        )

    def generate_set_variable_command(self) -> SetVariableCommand:
        """Generate set variable command."""
        styles = []
        if random.random() < 0.4:
            for _ in range(random.randint(1, 3)):
                styles.append(self.generate_inline_style_content())

        return SetVariableCommand(
            variable_name=random.choice(["station_name", "platform_number", "train_time", "temperature", "alert_message"]),
            variable_content=self.generate_bindable_text(include_bindings=False),
            variable_styles=styles
        )

    def generate_list_item(self) -> ListItem:
        """Generate list item."""
        styles = []
        if random.random() < 0.3:
            for _ in range(random.randint(1, 2)):
                styles.append(self.generate_inline_style_content())

        return ListItem(
            listitem_content=random.choice([
                "Platform 1 - Express to Central",
                "Platform 2 - Local service",
                "Platform 3 - Delayed 15 minutes",
                "Platform 4 - Cancelled",
                "Next departure: 14:30"
            ]),
            listitem_styles=styles
        )

    def generate_set_list_command(self) -> SetListCommand:
        """Generate set list command."""
        items = []
        for _ in range(random.randint(1, 5)):
            items.append(self.generate_list_item())

        return SetListCommand(
            setlist_name=random.choice(["departures", "arrivals", "alerts", "platforms"]),
            setlist_items=items
        )

    def generate_clear_content_command(self) -> ClearContentCommand:
        """Generate clear content command."""
        return ClearContentCommand(
            clear_target=self._next_element_id("target") if random.random() < 0.7 else None
        )

    def generate_content_state(self) -> ContentState:
        """Generate content state with various commands."""
        commands = []

        # Add some variable commands
        for _ in range(random.randint(1, 4)):
            commands.append(self.generate_set_variable_command())

        # Add some list commands
        for _ in range(random.randint(0, 2)):
            commands.append(self.generate_set_list_command())

        # Occasionally add clear command
        if random.random() < 0.3:
            commands.append(self.generate_clear_content_command())

        return ContentState(
            content_target_form=self._next_element_id("form"),
            content_target_view=self._next_element_id("view") if random.random() < 0.6 else None,
            content_commands=commands
        )


class EdgeCaseGenerator(TestDataGenerator):
    """Generates edge case test data for comprehensive testing."""

    def generate_minimal_layout(self) -> LayoutDefinition:
        """Generate minimal valid layout with empty collections."""
        return LayoutDefinition(
            layout_styles=[],
            layout_forms=[]
        )

    def generate_null_heavy_style(self) -> StyleDefinition:
        """Generate style with many null values."""
        return StyleDefinition(
            style_id=self._next_style_id("null_style"),
            style_font=None,
            style_text=None,
            style_border=None,
            style_background=None,
            style_alignment=None,
            style_animation=None
        )

    def generate_boundary_coordinates(self) -> Coordinates:
        """Generate coordinates with boundary values."""
        return Coordinates(
            top=65535,  # Max pixel value
            left=0,     # Min pixel value
            width=1,    # Min width
            height=65535  # Max height
        )

    def generate_max_font_size_style(self) -> FontStyleDef:
        """Generate font style with maximum values."""
        return FontStyleDef(
            font_color="ffffff",  # White
            font_family="A" * 100,  # Long font name
            font_size=9999,  # Max font size
            font_weight=FontWeight.W900,
            font_style=FontStyle.ITALIC,
            font_decoration=FontDecoration.UNDERLINE
        )

    def generate_empty_text_elements(self) -> List[Union[TextInput, TextDisplay]]:
        """Generate text elements with empty content."""
        return [
            TextInput(
                textinput_element_id=self._next_element_id("empty_input"),
                textinput_coordinates=self.generate_coordinates(),
                textinput_content=""
            ),
            TextDisplay(
                textdisplay_element_id=self._next_element_id("empty_display"),
                textdisplay_coordinates=self.generate_coordinates(),
                textdisplay_children=[]
            )
        ]

    def generate_deeply_nested_containers(self, depth: int = 5) -> PanelContainer:
        """Generate deeply nested container structure."""
        if depth <= 0:
            return PanelContainer(
                panel_element_id=self._next_element_id("deep_panel"),
                panel_coordinates=self.generate_coordinates(),
                panel_children=[]
            )

        child = self.generate_deeply_nested_containers(depth - 1)
        return PanelContainer(
            panel_element_id=self._next_element_id("deep_panel"),
            panel_coordinates=self.generate_coordinates(),
            panel_children=[child]
        )

    def generate_binding_variations(self) -> List[str]:
        """Generate various binding expression patterns."""
        return [
            "{simple_var}",
            "{@detection_var}",
            "{!negation_var}",
            "{var_with_underscores}",
            "{var123}",
            "{UPPERCASE_VAR}",
            "{mixed_Case_Var}",
            "Text with {embedded_var} binding",
            "Multiple {var1} and {var2} bindings",
            "{@complex_detection_123}",
            "{!complex_negation_456}"
        ]

    def generate_special_characters_content(self) -> List[str]:
        """Generate content with special characters."""
        return [
            "Special chars: !@#$%^&*()",
            "Unicode: àáâãäåæçèéêë",
            "Symbols: ←→↑↓⇐⇒⇑⇓",
            "Numbers: 0123456789",
            "Mixed: Train №123 → Platform Ⅰ",
            "Quotes: 'single' \"double\"",
            "Newlines:\nLine 1\nLine 2",
            "Tabs:\tTabbed\tContent"
        ]

    def generate_extreme_values_layout(self) -> LayoutDefinition:
        """Generate layout with extreme values for stress testing."""
        # Create many styles
        styles = []
        for i in range(100):  # Large number of styles
            styles.append(StyleDefinition(
                style_id=f"extreme_style_{i:03d}",
                style_font=self.generate_max_font_size_style() if i % 10 == 0 else None
            ))

        # Create form with many views
        views = []
        for i in range(10):  # Many views
            view = ViewContainer(
                view_element_id=f"extreme_view_{i:03d}",
                view_coordinates=self.generate_boundary_coordinates(),
                view_children=[self.generate_text_input() for _ in range(20)]  # Many children
            )
            views.append(view)

        form = FormDefinition(
            form_element_id="extreme_form",
            form_views=views
        )

        return LayoutDefinition(
            layout_styles=styles,
            layout_forms=[form]
        )


class RealWorldScenarioGenerator(TestDataGenerator):
    """Generates realistic railway display scenarios."""

    def generate_station_info_layout(self) -> LayoutDefinition:
        """Generate realistic station information display layout."""
        # Station-specific styles
        header_style = StyleDefinition(
            style_id="station_header",
            style_font=FontStyleDef(
                font_color="ffffff",
                font_family="Arial",
                font_size=48,
                font_weight=FontWeight.BOLD
            ),
            style_background=BackgroundStyleDef(
                background_color="003366"
            ),
            style_alignment=AlignmentStyleDef(
                align_horizontal=HorizontalAlignment.CENTER,
                align_vertical=VerticalAlignment.CENTER
            )
        )

        departure_style = StyleDefinition(
            style_id="departure_info",
            style_font=FontStyleDef(
                font_color="000000",
                font_family="Arial",
                font_size=24,
                font_weight=FontWeight.NORMAL
            ),
            style_animation=AnimationStyleDef(
                animation_type=AnimationType.SCROLL,
                animation_speed=50,
                animation_gap=100
            )
        )

        alert_style = StyleDefinition(
            style_id="alert_blink",
            style_font=FontStyleDef(
                font_color="ff0000",
                font_family="Arial",
                font_size=32,
                font_weight=FontWeight.BOLD
            ),
            style_animation=AnimationStyleDef(
                animation_type=AnimationType.BLINK,
                animation_speed=500,
                animation_duration=1000
            )
        )

        # Create main view
        header_text = TextDisplay(
            textdisplay_element_id="station_header",
            textdisplay_style_ref="station_header",
            textdisplay_coordinates=Coordinates(top=0, left=0, width=1920, height=100),
            textdisplay_children=[
                TextRun(
                    textrun_element_id="header_text",
                    textrun_content="{station_name} Railway Station"
                )
            ]
        )

        departure_board = TextDisplay(
            textdisplay_element_id="departure_board",
            textdisplay_style_ref="departure_info",
            textdisplay_coordinates=Coordinates(top=120, left=50, width=1820, height=600),
            textdisplay_type=TextBoxType.MULTILINE,
            textdisplay_children=[
                TextRun(
                    textrun_element_id="departures",
                    textrun_content="Next Departures:\n{departure_list}"
                )
            ]
        )

        alert_banner = TextDisplay(
            textdisplay_element_id="alert_banner",
            textdisplay_style_ref="alert_blink",
            textdisplay_visible="{@has_alerts}",
            textdisplay_coordinates=Coordinates(top=750, left=50, width=1820, height=80),
            textdisplay_children=[
                TextRun(
                    textrun_element_id="alert_text",
                    textrun_content="ALERT: {alert_message}"
                )
            ]
        )

        # Navigation buttons
        info_button = ButtonElement(
            button_element_id="info_button",
            button_number=1,
            button_command="switch view",
            button_target="info_view",
            button_params=[
                ButtonParameter(button_param_name="view", button_param_value="station_info")
            ]
        )

        main_view = ViewContainer(
            view_element_id="main_view",
            view_coordinates=Coordinates(top=0, left=0, width=1920, height=1080),
            view_is_default=True,
            view_home_timeout=300,
            view_children=[header_text, departure_board, alert_banner, info_button]
        )

        form = FormDefinition(
            form_element_id="station_display",
            form_views=[main_view]
        )

        return LayoutDefinition(
            layout_styles=[header_style, departure_style, alert_style],
            layout_forms=[form]
        )

    def generate_platform_display_content(self) -> ContentState:
        """Generate realistic platform display content state."""
        return ContentState(
            content_target_form="platform_display",
            content_target_view="main_view",
            content_commands=[
                SetVariableCommand(
                    variable_name="platform_number",
                    variable_content="Platform 3"
                ),
                SetVariableCommand(
                    variable_name="next_train",
                    variable_content="Express to Central - 14:25"
                ),
                SetVariableCommand(
                    variable_name="temperature",
                    variable_content="22°C"
                ),
                SetListCommand(
                    setlist_name="upcoming_trains",
                    setlist_items=[
                        ListItem(listitem_content="14:25 - Express to Central"),
                        ListItem(listitem_content="14:40 - Local to Suburbs"),
                        ListItem(listitem_content="15:00 - Express to Airport"),
                        ListItem(listitem_content="15:15 - Local Service")
                    ]
                )
            ]
        )
