#!/usr/bin/env python3
"""
Integration test runner for CSDIP Python ↔ TypeScript data fidelity testing.
Executes both Python and TypeScript test suites and compares results.
"""

import json
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class IntegrationTestRunner:
    """Runs and coordinates Python and TypeScript integration tests."""
    
    def __init__(self):
        self.test_dir = Path(__file__).parent
        self.results_dir = self.test_dir / "results"
        self.results_dir.mkdir(exist_ok=True)
        
        self.python_test_script = self.test_dir / "test_python_serialization.py"
        self.typescript_test_script = self.test_dir / "typescript_deserialization_test.ts"
        
        self.python_results_file = self.results_dir / "python_serialization_results.json"
        self.typescript_results_file = self.results_dir / "typescript_deserialization_results.json"
        self.integration_results_file = self.results_dir / "integration_test_results.json"
    
    def run_python_tests(self) -> Dict[str, Any]:
        """Run Python serialization tests."""
        print("=" * 60)
        print("Running Python serialization tests...")
        print("=" * 60)
        
        try:
            # Run Python test script
            result = subprocess.run(
                [sys.executable, str(self.python_test_script)],
                cwd=str(self.test_dir),
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode != 0:
                print(f"Python tests failed with return code {result.returncode}")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return {"success": False, "error": result.stderr}
            
            print("Python tests completed successfully")
            print("STDOUT:", result.stdout)
            
            # Load results
            if self.python_results_file.exists():
                with open(self.python_results_file, 'r', encoding='utf-8') as f:
                    python_results = json.load(f)
                return {"success": True, "results": python_results}
            else:
                return {"success": False, "error": "Python results file not found"}
                
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Python tests timed out"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def run_typescript_tests(self) -> Dict[str, Any]:
        """Run TypeScript deserialization tests."""
        print("=" * 60)
        print("Running TypeScript deserialization tests...")
        print("=" * 60)
        
        try:
            # Check if Node.js is available
            node_check = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if node_check.returncode != 0:
                return {"success": False, "error": "Node.js not found. Please install Node.js to run TypeScript tests."}
            
            print(f"Using Node.js version: {node_check.stdout.strip()}")
            
            # Check if TypeScript compiler is available
            tsc_check = subprocess.run(["npx", "tsc", "--version"], capture_output=True, text=True)
            if tsc_check.returncode != 0:
                print("TypeScript compiler not found, attempting to install...")
                # Try to install TypeScript locally
                install_result = subprocess.run(
                    ["npm", "install", "typescript", "@types/node"],
                    cwd=str(self.test_dir),
                    capture_output=True,
                    text=True
                )
                if install_result.returncode != 0:
                    return {"success": False, "error": "Failed to install TypeScript"}
            
            # Compile TypeScript to JavaScript
            print("Compiling TypeScript...")
            compile_result = subprocess.run(
                ["npx", "tsc", str(self.typescript_test_script), "--target", "es2020", "--module", "commonjs", "--moduleResolution", "node"],
                cwd=str(self.test_dir),
                capture_output=True,
                text=True
            )
            
            if compile_result.returncode != 0:
                print("TypeScript compilation failed:")
                print("STDOUT:", compile_result.stdout)
                print("STDERR:", compile_result.stderr)
                return {"success": False, "error": f"TypeScript compilation failed: {compile_result.stderr}"}
            
            # Run the compiled JavaScript
            js_file = self.typescript_test_script.with_suffix('.js')
            if not js_file.exists():
                return {"success": False, "error": "Compiled JavaScript file not found"}
            
            print("Running TypeScript tests...")
            result = subprocess.run(
                ["node", str(js_file)],
                cwd=str(self.test_dir),
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode != 0:
                print(f"TypeScript tests failed with return code {result.returncode}")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return {"success": False, "error": result.stderr}
            
            print("TypeScript tests completed successfully")
            print("STDOUT:", result.stdout)
            
            # Clean up compiled JavaScript file
            if js_file.exists():
                js_file.unlink()
            
            # Load results
            if self.typescript_results_file.exists():
                with open(self.typescript_results_file, 'r', encoding='utf-8') as f:
                    typescript_results = json.load(f)
                return {"success": True, "results": typescript_results}
            else:
                return {"success": False, "error": "TypeScript results file not found"}
                
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "TypeScript tests timed out"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def compare_results(self, python_results: Dict[str, Any], typescript_results: Dict[str, Any]) -> Dict[str, Any]:
        """Compare Python and TypeScript test results for data fidelity analysis."""
        print("=" * 60)
        print("Analyzing data fidelity between Python and TypeScript...")
        print("=" * 60)
        
        comparison = {
            "timestamp": datetime.now().isoformat(),
            "python_summary": python_results.get("summary", {}),
            "typescript_summary": typescript_results.get("summary", {}),
            "data_fidelity_analysis": {},
            "cross_validation_results": [],
            "issues_found": []
        }
        
        # Compare test counts and success rates
        py_total = python_results.get("summary", {}).get("total_tests", 0)
        ts_total = typescript_results.get("summary", {}).get("total_tests", 0)
        py_success_rate = python_results.get("summary", {}).get("success_rate", 0)
        ts_success_rate = typescript_results.get("summary", {}).get("success_rate", 0)
        
        comparison["data_fidelity_analysis"] = {
            "python_tests": py_total,
            "typescript_tests": ts_total,
            "python_success_rate": py_success_rate,
            "typescript_success_rate": ts_success_rate,
            "success_rate_difference": abs(py_success_rate - ts_success_rate),
            "both_platforms_successful": py_success_rate > 0.95 and ts_success_rate > 0.95
        }
        
        # Cross-validate specific test cases
        py_detailed = python_results.get("detailed_results", [])
        ts_detailed = typescript_results.get("detailed_results", [])
        
        # Find matching test cases (TypeScript tests that used Python-generated data)
        for ts_result in ts_detailed:
            if ts_result.get("testName", "").startswith("python_"):
                # Find corresponding Python test
                py_test_name = ts_result["testName"].replace("python_", "")
                py_result = next((r for r in py_detailed if r.get("test_name") == py_test_name), None)
                
                if py_result:
                    cross_validation = {
                        "test_name": py_test_name,
                        "python_success": py_result.get("success", False),
                        "typescript_success": ts_result.get("success", False),
                        "python_data_integrity": py_result.get("data_integrity", False),
                        "typescript_type_check": ts_result.get("typeCheckPassed", False),
                        "json_size_match": py_result.get("json_size") == ts_result.get("jsonSize"),
                        "field_count_match": py_result.get("field_count") == ts_result.get("fieldCount")
                    }
                    
                    # Check for issues
                    if not cross_validation["python_success"] or not cross_validation["typescript_success"]:
                        comparison["issues_found"].append(f"Test {py_test_name}: Platform compatibility issue")
                    
                    if cross_validation["python_success"] and not cross_validation["python_data_integrity"]:
                        comparison["issues_found"].append(f"Test {py_test_name}: Python data integrity failure")
                    
                    if cross_validation["typescript_success"] and not cross_validation["typescript_type_check"]:
                        comparison["issues_found"].append(f"Test {py_test_name}: TypeScript type validation failure")
                    
                    if not cross_validation["json_size_match"]:
                        comparison["issues_found"].append(f"Test {py_test_name}: JSON size mismatch")
                    
                    if not cross_validation["field_count_match"]:
                        comparison["issues_found"].append(f"Test {py_test_name}: Field count mismatch")
                    
                    comparison["cross_validation_results"].append(cross_validation)
        
        # Calculate overall data fidelity score
        if comparison["cross_validation_results"]:
            successful_cross_validations = len([
                cv for cv in comparison["cross_validation_results"]
                if cv["python_success"] and cv["typescript_success"] and 
                   cv["python_data_integrity"] and cv["typescript_type_check"]
            ])
            
            comparison["data_fidelity_analysis"]["cross_validation_success_rate"] = (
                successful_cross_validations / len(comparison["cross_validation_results"])
            )
        else:
            comparison["data_fidelity_analysis"]["cross_validation_success_rate"] = 0
        
        # Overall assessment
        fidelity_score = comparison["data_fidelity_analysis"]["cross_validation_success_rate"]
        if fidelity_score >= 0.95:
            comparison["data_fidelity_analysis"]["assessment"] = "EXCELLENT - 100% data fidelity achieved"
        elif fidelity_score >= 0.90:
            comparison["data_fidelity_analysis"]["assessment"] = "GOOD - Minor data fidelity issues"
        elif fidelity_score >= 0.75:
            comparison["data_fidelity_analysis"]["assessment"] = "FAIR - Some data fidelity concerns"
        else:
            comparison["data_fidelity_analysis"]["assessment"] = "POOR - Significant data fidelity issues"
        
        return comparison
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run complete integration test suite."""
        print("Starting CSDIP Python ↔ TypeScript Integration Tests")
        print(f"Test directory: {self.test_dir}")
        print(f"Results directory: {self.results_dir}")
        
        start_time = time.time()
        
        # Run Python tests
        python_result = self.run_python_tests()
        
        # Run TypeScript tests
        typescript_result = self.run_typescript_tests()
        
        # Prepare final results
        integration_results = {
            "timestamp": datetime.now().isoformat(),
            "duration_seconds": time.time() - start_time,
            "python_test_result": python_result,
            "typescript_test_result": typescript_result,
            "overall_success": python_result.get("success", False) and typescript_result.get("success", False)
        }
        
        # If both test suites succeeded, compare results
        if integration_results["overall_success"]:
            comparison = self.compare_results(
                python_result["results"],
                typescript_result["results"]
            )
            integration_results["data_fidelity_comparison"] = comparison
        
        # Save integration results
        with open(self.integration_results_file, 'w', encoding='utf-8') as f:
            json.dump(integration_results, f, indent=2, ensure_ascii=False)
        
        return integration_results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print test summary to console."""
        print("\n" + "=" * 80)
        print("CSDIP INTEGRATION TEST SUMMARY")
        print("=" * 80)
        
        print(f"Duration: {results['duration_seconds']:.2f} seconds")
        print(f"Overall Success: {'✓' if results['overall_success'] else '✗'}")
        
        # Python results
        py_result = results["python_test_result"]
        print(f"\nPython Tests: {'✓' if py_result.get('success') else '✗'}")
        if py_result.get("success") and "results" in py_result:
            py_summary = py_result["results"]["summary"]
            print(f"  - Total: {py_summary['total_tests']}")
            print(f"  - Success Rate: {py_summary['success_rate']:.2%}")
            print(f"  - Data Integrity Issues: {py_summary['data_integrity_issues']}")
        
        # TypeScript results
        ts_result = results["typescript_test_result"]
        print(f"\nTypeScript Tests: {'✓' if ts_result.get('success') else '✗'}")
        if ts_result.get("success") and "results" in ts_result:
            ts_summary = ts_result["results"]["summary"]
            print(f"  - Total: {ts_summary['total_tests']}")
            print(f"  - Success Rate: {ts_summary['success_rate']:.2%}")
            print(f"  - Type Check Failures: {ts_summary['type_check_failures']}")
        
        # Data fidelity analysis
        if "data_fidelity_comparison" in results:
            fidelity = results["data_fidelity_comparison"]["data_fidelity_analysis"]
            print(f"\nData Fidelity Analysis:")
            print(f"  - Assessment: {fidelity['assessment']}")
            print(f"  - Cross-validation Success: {fidelity['cross_validation_success_rate']:.2%}")
            
            issues = results["data_fidelity_comparison"]["issues_found"]
            if issues:
                print(f"  - Issues Found: {len(issues)}")
                for issue in issues[:5]:  # Show first 5 issues
                    print(f"    • {issue}")
                if len(issues) > 5:
                    print(f"    ... and {len(issues) - 5} more")
            else:
                print("  - No data fidelity issues found! 🎉")
        
        print(f"\nResults saved to: {self.integration_results_file}")
        print("=" * 80)


if __name__ == "__main__":
    runner = IntegrationTestRunner()
    results = runner.run_all_tests()
    runner.print_summary(results)
