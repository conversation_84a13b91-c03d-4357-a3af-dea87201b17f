# CSDIP Integration Tests

Comprehensive integration tests for the CSDIP (railway display system) Python ↔ TypeScript data conversion pipeline.

## Overview

This test suite validates the complete data conversion pipeline:

```
Python Pydantic Models → JSON Schema → TypeScript Types → JSON Data → Python Models
```

The tests ensure **bidirectional data integrity** with no information loss during the conversion process.

## Test Architecture

### 1. Test Data Generators (`test_data_generators.py`)

- **`TestDataGenerator`**: Creates realistic CSDIP protocol data based on `csdip_protocol.md`
- **`EdgeCaseGenerator`**: Generates edge cases (null values, boundary conditions, empty collections)
- **`RealWorldScenarioGenerator`**: Creates realistic railway display scenarios (station info, platform displays)

### 2. Python Test Suite (`test_python_serialization.py`)

Tests Python model → JSON serialization:
- Creates model instances using test data generators
- Serializes to JSON using Pydantic's `model_dump()`
- Validates JSON can be deserialized back to identical models
- Tests data integrity (original == recreated)

### 3. TypeScript Test Suite (`typescript_deserialization_test.ts`)

Tests JSON → TypeScript deserialization:
- Loads JSON data from Python test results
- Validates TypeScript type structure compliance
- Performs type-safe operations to verify TypeScript compatibility
- Tests manual TypeScript-friendly data

### 4. Integration Test Runner (`run_integration_tests.py`)

Orchestrates the complete test pipeline:
- Runs Python serialization tests
- Runs TypeScript deserialization tests
- Cross-validates results between platforms
- Analyzes data fidelity and reports issues

## Test Categories

### Basic Elements
- `TextInput`, `TextDisplay`, `ImageDisplay`, `VideoDisplay`, `HTMLDisplay`
- `ButtonElement`, `PanelContainer`, `StackPanelContainer`
- `ViewContainer`, `FormDefinition`, `StyleDefinition`, `ContentState`

### Complex Layouts
- Multi-form layouts with nested containers
- Complex styling with animations and alignments
- Real-world railway display scenarios

### Edge Cases
- Minimal layouts with empty collections
- Null-heavy styles and optional fields
- Boundary values (max pixel sizes, font sizes)
- Deeply nested container structures
- Special characters and Unicode content

### Binding Expressions
- Variable bindings: `{variable_name}`
- Detection bindings: `{@detection_var}`
- Negation bindings: `{!negation_var}`
- Mixed content with embedded bindings

## Running the Tests

### Prerequisites

1. **Python 3.8+** with required packages:
   ```bash
   pip install pydantic
   ```

2. **Node.js 16+** for TypeScript tests:
   ```bash
   npm install -g typescript
   npm install @types/node
   ```

### Quick Start

Run the complete integration test suite:

```bash
cd tests/integration
python run_integration_tests.py
```

### Individual Test Suites

Run Python tests only:
```bash
python test_python_serialization.py
```

Run TypeScript tests only:
```bash
npx tsc typescript_deserialization_test.ts --target es2020 --module commonjs
node typescript_deserialization_test.js
```

## Test Results

Results are saved to `tests/integration/results/`:

- `python_serialization_results.json` - Python test results
- `typescript_deserialization_results.json` - TypeScript test results  
- `integration_test_results.json` - Combined analysis and data fidelity report

### Result Structure

```json
{
  "summary": {
    "total_tests": 150,
    "successful_tests": 148,
    "success_rate": 0.9867,
    "data_integrity_issues": 0
  },
  "data_fidelity_analysis": {
    "assessment": "EXCELLENT - 100% data fidelity achieved",
    "cross_validation_success_rate": 1.0,
    "both_platforms_successful": true
  },
  "issues_found": []
}
```

## Data Fidelity Validation

The tests validate several aspects of data fidelity:

### 1. Serialization Integrity
- Python model → JSON → Python model (round-trip)
- All fields preserved with correct types
- No data loss or corruption

### 2. Type Safety
- JSON data conforms to TypeScript interface structure
- Required fields are present
- Optional fields handle null/undefined correctly
- Numeric ranges respect validation constraints

### 3. Cross-Platform Compatibility
- Same JSON data works in both Python and TypeScript
- Field counts and JSON sizes match between platforms
- Binding expressions preserved correctly

### 4. Edge Case Handling
- Empty collections serialize/deserialize correctly
- Null values handled consistently
- Boundary values (0, 65535) work correctly
- Special characters preserved in UTF-8

## Expected Results

A successful test run should show:

- **Python Success Rate**: >99% (only extreme edge cases may fail)
- **TypeScript Success Rate**: >95% (type validation is stricter)
- **Data Fidelity Score**: >95% (cross-platform compatibility)
- **Assessment**: "EXCELLENT" or "GOOD"

## Common Issues and Solutions

### Python Test Failures
- **Import errors**: Ensure project root is in Python path
- **Validation errors**: Check Pydantic model field constraints
- **JSON serialization**: Verify all model fields are JSON-serializable

### TypeScript Test Failures
- **Compilation errors**: Check TypeScript version and type imports
- **Type validation**: Verify generated types match JSON structure
- **Node.js errors**: Ensure Node.js 16+ is installed

### Data Fidelity Issues
- **Field count mismatches**: Check for missing/extra fields in conversion
- **Type conversion errors**: Verify numeric ranges and string formats
- **Binding expression corruption**: Check special character handling

## Extending the Tests

### Adding New Test Cases

1. **Add to generators**: Extend `TestDataGenerator` with new methods
2. **Update Python tests**: Add new test cases to `PythonSerializationTester`
3. **Update TypeScript tests**: Add validation for new types
4. **Run integration**: Verify cross-platform compatibility

### Adding New Element Types

1. **Update generators**: Add generation methods for new elements
2. **Add validation**: Implement TypeScript validation for new types
3. **Test edge cases**: Ensure new elements handle edge cases correctly

## Continuous Integration

For CI/CD integration:

```bash
# Run tests with exit codes
python run_integration_tests.py
if [ $? -eq 0 ]; then
    echo "Integration tests passed"
else
    echo "Integration tests failed"
    exit 1
fi
```

## Performance Metrics

Typical test execution times:
- Python tests: 10-30 seconds
- TypeScript tests: 15-45 seconds  
- Total integration: 30-90 seconds

Memory usage:
- Peak memory: ~100MB
- JSON file sizes: 1KB-50KB per test case

## Troubleshooting

### Debug Mode
Set environment variable for verbose output:
```bash
export CSDIP_TEST_DEBUG=1
python run_integration_tests.py
```

### Manual Validation
Inspect individual test results:
```bash
cat tests/integration/results/python_serialization_results.json | jq '.failed_tests'
cat tests/integration/results/typescript_deserialization_results.json | jq '.type_check_failures'
```

### Schema Validation
Verify the generated JSON schema:
```bash
python scripts/generate_unified_schema.py
cat csdip-unified.schema.json | jq '.definitions'
```

## Contributing

When adding new features to the CSDIP protocol:

1. Update the Pydantic models in `types_unified.py`
2. Regenerate the JSON schema and TypeScript types
3. Add test cases to the integration test suite
4. Verify data fidelity across the conversion pipeline
5. Update this documentation

## License

This test suite is part of the CSDIP display server project.
